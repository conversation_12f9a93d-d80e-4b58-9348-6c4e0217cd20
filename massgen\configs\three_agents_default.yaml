# MassGen Three Agent Configuration
# Gemini-2.5-flash, GPT-5-nano, and Grok-3-mini with builtin tools enabled

agents:
  - id: "gemini2.5flash"
    backend:
      type: "gemini"
      model: "gemini-2.5-flash"
      enable_web_search: true
      # enable_code_execution: true
    # system_message: "You are a helpful AI assistant with web search and code execution capabilities. For any question involving current events, recent information, or real-time data, ALWAYS use web search first."

  - id: "gpt5nano"
    backend:
      type: "openai"
      model: "gpt-5-nano"
      text:
        verbosity: "medium"
      reasoning:
        effort: "medium"
        summary: "auto"
      enable_web_search: true
      enable_code_interpreter: true
    # system_message: "You are a helpful AI assistant with web search and code execution capabilities. For any question involving current events, recent information, or real-time data, ALWAYS use web search first."

  - id: "grok3mini"
    backend:
      type: "grok"
      model: "grok-3-mini"
      enable_web_search: true
    # system_message: "You are a helpful AI assistant with web search capabilities. For any question involving current events, recent information, or real-time data, ALWAYS use web search first."

ui:
  display_type: "rich_terminal"
  logging_enabled: true