agents:
  - id: "claude_code_discord_mcp"
    backend:
      type: "claude_code"
      cwd: "claude_code_workspace_discord_mcp"
      permission_mode: "bypassPermissions"
      
      # Discord MCP server 
      mcp_servers:
        discord:
          type: "stdio"
          command: "npx"
          args: ["-y", "mcp-discord", "--config", "YOUR_DISCORD_TOKEN"]

      allowed_tools:
        - "Read"
        - "Write"
        - "Bash"
        - "LS"
        - "WebSearch"
        # MCP tools will be auto-discovered from the server
    system_message: |
      You are a helpful assistant with access to built-in tools and MCP servers. Before using any tools, check if the task is already completed by previous steps or agents. Only use tools for unfinished tasks. Never duplicate actions, especially communications (messages, emails) or workspace modifications (file edits, Discord/Slack posts). For these critical operations, first provide the content as your answer, then execute the tools only during final presentation.

  - id: "gpt-5-mini"
    backend:
      type: "openai"
      model: "gpt-5-mini"
      reasoning:
        effort: "medium"
        summary: "auto"
      enable_web_search: true
      enable_code_interpreter: true
    system_message: "You are a helpful AI assistant with web search and code execution capabilities. You should always complete tasks that you can do. If you cannot complete a task, just omit it."

ui:
  display_type: "rich_terminal"
  logging_enabled: true