<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MassGen: Scaling AI Through Multi-Agent Collaboration | DataHack Summit 2025</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea, #764ba2);
            overflow: hidden;
        }

        .slideshow-container {
            position: relative;
            width: 100vw;
            height: 100vh;
        }

        .slide {
            display: none;
            position: absolute;
            width: 100%;
            height: 100%;
            background: white;
            padding: 60px;
            box-sizing: border-box;
            animation: fadeIn 0.5s ease-in-out;
        }

        .slide.active {
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateX(30px); }
            to { opacity: 1; transform: translateX(0); }
        }

        .slide h1 {
            font-size: 3.5em;
            color: #2c3e50;
            margin-bottom: 30px;
            text-align: center;
            font-weight: 700;
        }

        .slide h2 {
            font-size: 2.8em;
            color: #34495e;
            margin-bottom: 40px;
            text-align: center;
            font-weight: 600;
        }

        .slide .subtitle {
            font-size: 1.4em;
            color: #2c3e50;
            text-align: center;
            margin-bottom: 40px;
            font-weight: 600;
        }

        .slide .speaker-info {
            font-size: 1.3em;
            color: #2c3e50;
            text-align: center;
            margin-bottom: 20px;
            font-weight: 500;
        }

        .slide .summit-info {
            font-size: 1.1em;
            color: #3498db;
            text-align: center;
            margin-bottom: 20px;
            font-family: 'Courier New', monospace;
        }

        .slide ul {
            font-size: 1.8em;
            line-height: 1.8;
            color: #2c3e50;
            list-style: none;
            max-width: 1000px;
            margin: 0 auto;
        }

        .slide li {
            margin-bottom: 25px;
            padding-left: 40px;
            position: relative;
        }

        .slide li::before {
            content: "→";
            position: absolute;
            left: 0;
            color: #3498db;
            font-weight: bold;
            font-size: 1.2em;
        }

        .navigation {
            position: fixed;
            bottom: 30px;
            right: 30px;
            z-index: 1000;
        }

        .nav-btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 12px 20px;
            margin: 0 5px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .nav-btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }

        .nav-btn:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
            transform: none;
        }

        .slide-counter {
            position: fixed;
            bottom: 30px;
            left: 30px;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 10px 20px;
            border-radius: 20px;
            font-size: 16px;
            max-width: 300px;
        }

        .slide-title {
            font-size: 12px;
            opacity: 0.8;
            margin-top: 5px;
        }

        .slide-nav {
            position: fixed;
            top: 30px;
            right: 30px;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 15px;
            border-radius: 10px;
            font-size: 14px;
        }

        .nav-grid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 8px;
            max-width: 250px;
        }

        .nav-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: rgba(255,255,255,0.3);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .nav-dot.active {
            background: #3498db;
            transform: scale(1.3);
        }

        .nav-dot:hover {
            background: rgba(255,255,255,0.6);
        }

        .icon {
            font-size: 4em;
            text-align: center;
            margin-bottom: 30px;
        }

        .code-snippet {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            width: 95%;
            max-width: 1200px;
            font-size: 1.1em;
            margin: 20px auto;
        }

        .highlight {
            color: #e74c3c;
            font-weight: bold;
        }

        .architecture-diagram {
            width: 100%;
            height: 60vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
            margin-top: 20px;
        }

        .arch-flow {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
            width: 100%;
            max-width: 1000px;
        }

        .arch-component {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            min-width: 180px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            transition: transform 0.3s ease;
        }

        .arch-component:hover {
            transform: translateY(-3px);
        }

        .orchestrator {
            background: linear-gradient(135deg, #74b9ff, #0984e3);
        }

        .agent {
            background: linear-gradient(135deg, #fd79a8, #e84393);
            min-width: 160px;
            padding: 20px;
        }

        .hub {
            background: linear-gradient(135deg, #00b894, #00cec9);
        }

        .agents-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 15px;
        }

        .agents-row {
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }

        .collaboration-note {
            font-size: 1.2em;
            color: #2c3e50;
            font-weight: bold;
            text-align: center;
            background: rgba(116, 185, 255, 0.1);
            padding: 10px 20px;
            border-radius: 25px;
            border: 2px dashed #74b9ff;
        }

        .component-icon {
            font-size: 1.8em;
            margin-bottom: 8px;
        }

        .component-title {
            font-size: 1.1em;
            font-weight: bold;
            margin-bottom: 6px;
        }

        .component-desc {
            font-size: 0.9em;
            opacity: 0.9;
        }

        .flow-arrow {
            font-size: 3em;
            color: #3498db;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .demo-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 30px;
            margin: 20px 0;
        }

        .demo-box {
            background: linear-gradient(135deg, #fd79a8, #e84393);
            color: white;
            padding: 25px;
            border-radius: 15px;
            font-size: 1.1em;
        }

        .metrics-comparison {
            display: flex;
            justify-content: space-around;
            margin: 30px 0;
            gap: 30px;
        }

        .metrics-box {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            flex: 1;
            border: 3px solid;
        }

        .metrics-box.single {
            border-color: #3498db;
        }

        .metrics-box.multi {
            border-color: #e74c3c;
        }

        .metrics-title {
            font-weight: bold;
            margin-bottom: 15px;
            font-size: 1.2em;
        }

        .metrics-value {
            font-size: 2.5em;
            font-weight: bold;
            margin: 15px 0;
        }

        .version-evolution {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 30px;
            margin: 30px 0;
        }

        .version-box {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
        }

        .version-box.current {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            transform: scale(1.05);
        }

        .call-to-action {
            background: linear-gradient(135deg, #00b894, #00cec9);
            color: white;
            padding: 40px;
            border-radius: 20px;
            text-align: center;
            margin: 30px 0;
        }

        .cta-links {
            display: flex;
            justify-content: space-around;
            margin-top: 30px;
            flex-wrap: wrap;
        }

        .cta-link {
            background: rgba(255,255,255,0.2);
            padding: 15px 30px;
            border-radius: 25px;
            text-decoration: none;
            color: white;
            font-weight: bold;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .cta-link:hover {
            background: rgba(255,255,255,0.5);
            transform: translateY(-3px);
        }

        .inline-link {
            color: #3498db;
            text-decoration: underline;
            font-weight: bold;
        }

        .inline-link:hover {
            color: #2980b9;
        }

        .version-badge {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            font-size: 1.1em;
            font-weight: bold;
            display: inline-block;
            margin-top: 20px;
            animation: pulse 2s infinite;
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 30px;
            margin: 30px 0;
        }

        .feature-card {
            background: rgba(255,255,255,0.1);
            border: 2px solid #3498db;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
        }

        .feature-icon {
            font-size: 3em;
            margin-bottom: 15px;
        }

        .datahack-logo {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 20px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="slideshow-container">
        <!-- Slide 1: Title -->
        <div class="slide active">
            <div class="datahack-logo">
                <div style="font-size: 2em; color: #e74c3c; font-weight: bold;">📊 DataHack Summit 2025</div>
            </div>
            <div style="display: flex; align-items: center; justify-content: center; gap: 40px; margin: 30px 0;">
                <div>
                    <img src="https://github.com/Leezekun/MassGen/blob/main/assets/logo.png?raw=true" alt="MassGen Logo" style="width: 180px; height: 180px; object-fit: contain;">
                </div>
                <div>
                    <img src="https://docs.ag2.ai/latest/docs/blog/2025-04-16-Reasoning/img/threads.jpeg" alt="Multi-Agent Threads" style="width: 200px; height: 180px; object-fit: cover; border-radius: 15px; box-shadow: 0 8px 25px rgba(0,0,0,0.15);">
                </div>
            </div>
            <div class="subtitle">Scaling AI Through Multi-Agent Collaboration</div>
            <!-- <div class="speaker-info">Chi Wang</div> -->
            <div class="summit-info">🌐 <a href="https://massgen.ai" style="color: #3498db; text-decoration: none;">massgen.ai</a> | <a href="https://github.com/Leezekun/MassGen" style="color: #3498db; text-decoration: none;">GitHub</a></div>
        </div>

        <!-- Slide 2: The Problem -->
        <div class="slide">
            <div class="icon">🚫</div>
            <h2>The Single-Agent Limitation</h2>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 40px; align-items: center;">
                <div>
                    <ul style="font-size: 1.6em;">
                        <li><strong>Siloed Thinking:</strong> Single models miss diverse perspectives</li>
                        <li><strong>Limited Context:</strong> No peer review or validation</li>
                        <li><strong>Sequential Processing:</strong> Linear, not parallel exploration</li>
                        <li><strong>Fixed Approach:</strong> Limited mid-task adaptation to new information</li>
                    </ul>
                </div>
                <div style="text-align: center;">
                    <img src="https://docs.ag2.ai/latest/docs/blog/2025-04-16-Reasoning/img/partner.jpg" alt="Single Agent Limitation" style="width: 100%; max-width: 400px; border-radius: 15px; box-shadow: 0 8px 25px rgba(0,0,0,0.15);">
                    <div style="margin-top: 15px; font-size: 1.2em; color: #7f8c8d; font-style: italic;">
                        From Isolation to Collaboration
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 3: The Solution - Multi-Agent Collaboration -->
        <div class="slide">
            <div class="icon">🤝</div>
            <h2>The Promise of Multi-Agent Collaboration</h2>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 40px; align-items: center;">
                <div>
                    <ul style="font-size: 1.6em;">
                        <li><strong>Study Group Dynamics:</strong> Like humans collaborating on complex problems</li>
                        <li><strong>Cross-Model Synergy:</strong> Leverage unique strengths of Claude, Gemini, GPT, Grok</li>
                        <li><strong>Parallel Processing:</strong> Multiple perspectives tackle same task simultaneously</li>
                        <li><strong>Real-time Intelligence Sharing:</strong> Agents learn and adapt from each other</li>
                    </ul>
                </div>
                <div style="text-align: center;">
                    <img src="https://docs.ag2.ai/latest/docs/blog/2025-04-16-Reasoning/img/cognition.jpg" alt="Cognition and Reasoning Process" style="width: 100%; max-width: 400px; border-radius: 15px; box-shadow: 0 8px 25px rgba(0,0,0,0.15);">
                    <div style="margin-top: 15px; font-size: 1.1em; color: #2c3e50;">
                        <div style="font-style: italic; margin-bottom: 10px;">The Promise of Collaborative Reasoning</div>
                        <div style="font-size: 0.9em; color: #3498db;">
                            📖 <a href="https://root.massgen.ai" style="color: #3498db; text-decoration: none;">Read our article: "Myth of Reasoning"</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 4: Evidence - Performance Gains -->
        <div class="slide">
            <div class="icon">📈</div>
            <h2>Proven Performance Gains - Grok Heavy Evidence</h2>
            <div class="metrics-comparison">
                <div class="metrics-box single">
                    <div class="metrics-title" style="color: #3498db;">Grok-4 Standard</div>
                    <div style="width: 60px; height: 60px; background: linear-gradient(135deg, #3498db, #2980b9); border-radius: 50%; margin: 0 auto 15px; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">1</div>
                    <div>Single Agent Processing</div>
                    <div class="metrics-value" style="color: #3498db;">38.6%</div>
                    <div style="color: #666; font-size: 1em;">Last Human Exam Score</div>
                    <div style="color: #666; font-size: 1em; margin-top: 8px;">$30/month</div>
                </div>
                <div class="metrics-box multi">
                    <div class="metrics-title" style="color: #e74c3c;">Grok-4 Heavy</div>
                    <div style="display: flex; justify-content: center; gap: 8px; margin-bottom: 15px;">
                        <div style="width: 40px; height: 40px; background: linear-gradient(135deg, #e74c3c, #c0392b); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 0.8em;">A1</div>
                        <div style="width: 40px; height: 40px; background: linear-gradient(135deg, #e74c3c, #c0392b); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 0.8em;">A2</div>
                        <div style="width: 40px; height: 40px; background: linear-gradient(135deg, #e74c3c, #c0392b); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 0.8em;">A3</div>
                    </div>
                    <div>Multi-Agent Collaboration</div>
                    <div class="metrics-value" style="color: #e74c3c;">44.4%</div>
                    <div style="color: #666; font-size: 1em;">Last Human Exam Score</div>
                    <div style="color: #666; font-size: 1em; margin-top: 8px;">$300/month</div>
                </div>
            </div>
            <div style="text-align: center; margin-top: 30px; padding: 20px; background: #fff3cd; border-left: 4px solid #f39c12; border-radius: 8px;">
                <strong style="font-size: 1.4em; color: #f39c12;">+15% Performance Boost</strong><br>
                <span style="color: #856404;">Multi-agent "study group" approach outperforms single agent</span><br><br>
                <span style="color: #666; font-style: italic;">"The exploration of the art & science of multi-agent collaboration has just started."</span>
            </div>
        </div>

        <!-- Slide 5: Architecture -->
        <div class="slide">
            <div class="architecture-diagram" style="height: 70vh; margin-top: 0;">
                <div class="arch-flow">
                    <div class="arch-component orchestrator">
                        <div class="component-icon">🚀</div>
                        <div class="component-title">MassGen Orchestrator</div>
                        <div class="component-desc">Task Distribution & Coordination</div>
                    </div>
                    
                    <div class="flow-arrow">↓</div>
                    
                    <div class="agents-container">
                        <div class="agents-row">
                            <div class="arch-component agent">
                                <div class="component-icon">🏗️</div>
                                <div class="component-title">Agent 1</div>
                                <div class="component-desc">Anthropic/Claude</div>
                            </div>
                            <div class="arch-component agent">
                                <div class="component-icon">👨‍💻</div>
                                <div class="component-title">Agent 2</div>
                                <div class="component-desc">Claude Code</div>
                            </div>
                            <div class="arch-component agent">
                                <div class="component-icon">🌟</div>
                                <div class="component-title">Agent 3</div>
                                <div class="component-desc">Google/Gemini</div>
                            </div>
                            <div class="arch-component agent">
                                <div class="component-icon">🤖</div>
                                <div class="component-title">Agent 4</div>
                                <div class="component-desc">OpenAI/GPT</div>
                            </div>
                            <div class="arch-component agent">
                                <div class="component-icon">⚡</div>
                                <div class="component-title">Agent 5</div>
                                <div class="component-desc">xAI/Grok</div>
                            </div>
                        </div>
                        <div class="collaboration-note">↕ Real-time Collaboration ↕</div>
                    </div>
                    
                    <div class="flow-arrow">↓</div>
                    
                    <div class="arch-component hub">
                        <div class="component-icon">🔄</div>
                        <div class="component-title">Shared Collaboration Hub</div>
                        <div class="component-desc">Real-time Notification & Consensus</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 6: Key Features & Capabilities -->
        <div class="slide">
            <h2>Key Features & Capabilities</h2>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 40px; align-items: center;">
                <div>
                    <ul style="font-size: 1.6em;">
                        <li><strong>🤝 Cross-Model Synergy:</strong> Diverse AI models working together</li>
                        <li><strong>⚡ Parallel Processing:</strong> Simultaneous problem-solving</li>
                        <li><strong>🔄 Iterative Refinement:</strong> Continuous improvement cycles</li>
                        <li><strong>👥 Real-time Sharing:</strong> Live collaboration between agents</li>
                        <li><strong>🎯 Consensus Building:</strong> Natural convergence</li>
                    </ul>
                </div>
                <div style="text-align: center;">
                    <img src="https://docs.ag2.ai/latest/docs/blog/2025-04-16-Reasoning/img/iter.jpg" alt="Iterative Refinement Process" style="width: 100%; max-width: 400px; border-radius: 15px; box-shadow: 0 8px 25px rgba(0,0,0,0.15);">
                    <div style="margin-top: 15px; font-size: 1em; color: #7f8c8d; font-style: italic;">
                        Iterative Refinement: The Reality of Reasoning
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 7: Tech Deep Dive - Async Streaming Architecture -->
        <div class="slide">
            <div class="icon">⚙️</div>
            <h2>Tech Deep Dive: Async Streaming & Dynamic Scheduling</h2>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 40px; align-items: center;">
                <div>
                    <ul style="font-size: 1.4em;">
                        <li><strong>🔄 Async Streaming:</strong> Real-time from 5+ agents</li>
                        <li><strong>⚡ Dynamic Scheduling:</strong> Smart start/stop coordination</li>
                        <li><strong>🔁 Graceful Restarts:</strong> Seamless task transitions</li>
                    </ul>
                </div>
                <div style="text-align: center;">
                    <svg viewBox="0 0 500 400" xmlns="http://www.w3.org/2000/svg" style="width: 100%; max-width: 450px; height: auto;">
                        <!-- Orchestrator -->
                        <rect x="200" y="20" width="100" height="40" rx="5" fill="#74b9ff" stroke="#0984e3" stroke-width="2"/>
                        <text x="250" y="45" text-anchor="middle" font-family="Arial" font-size="16" font-weight="bold" fill="white">Orchestrator</text>
                        
                        <!-- Agent streams -->
                        <rect x="50" y="120" width="80" height="30" rx="3" fill="#fd79a8" stroke="#e84393" stroke-width="2"/>
                        <text x="90" y="140" text-anchor="middle" font-family="Arial" font-size="14" fill="white">Agent 1</text>
                        
                        <rect x="150" y="120" width="80" height="30" rx="3" fill="#fd79a8" stroke="#e84393" stroke-width="2"/>
                        <text x="190" y="140" text-anchor="middle" font-family="Arial" font-size="14" fill="white">Agent 2</text>
                        
                        <rect x="250" y="120" width="80" height="30" rx="3" fill="#fd79a8" stroke="#e84393" stroke-width="2"/>
                        <text x="290" y="140" text-anchor="middle" font-family="Arial" font-size="14" fill="white">Agent 3</text>
                        
                        <rect x="350" y="120" width="80" height="30" rx="3" fill="#fd79a8" stroke="#e84393" stroke-width="2"/>
                        <text x="390" y="140" text-anchor="middle" font-family="Arial" font-size="14" fill="white">Agent 4</text>
                        
                        <!-- Streaming arrows -->
                        <line x1="250" y1="70" x2="90" y2="110" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
                        <line x1="250" y1="70" x2="190" y2="110" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
                        <line x1="250" y1="70" x2="290" y2="110" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
                        <line x1="250" y1="70" x2="390" y2="110" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
                        
                        <!-- StreamChunk types -->
                        <rect x="50" y="180" width="100" height="25" rx="3" fill="#00b894" stroke="#00cec9" stroke-width="1"/>
                        <text x="100" y="198" text-anchor="middle" font-family="Arial" font-size="13" fill="white">content</text>
                        
                        <rect x="160" y="180" width="100" height="25" rx="3" fill="#fdcb6e" stroke="#e17055" stroke-width="1"/>
                        <text x="210" y="198" text-anchor="middle" font-family="Arial" font-size="13" fill="white">tool_calls</text>
                        
                        <rect x="270" y="180" width="100" height="25" rx="3" fill="#a29bfe" stroke="#6c5ce7" stroke-width="1"/>
                        <text x="320" y="198" text-anchor="middle" font-family="Arial" font-size="13" fill="white">reasoning</text>
                        
                        <!-- Restart mechanism -->
                        <text x="250" y="240" text-anchor="middle" font-family="Arial" font-size="15" font-weight="bold" fill="#e74c3c">🔁 Restart Trigger</text>
                        <text x="250" y="260" text-anchor="middle" font-family="Arial" font-size="14" fill="#666">When Agent 2 provides new_answer</text>
                        
                        <!-- Restart arrows -->
                        <path d="M 190 280 Q 140 300 90 280" stroke="#e74c3c" stroke-width="2" fill="none" marker-end="url(#arrowhead-red)"/>
                        <path d="M 190 280 Q 240 300 290 280" stroke="#e74c3c" stroke-width="2" fill="none" marker-end="url(#arrowhead-red)"/>
                        <path d="M 190 280 Q 340 320 390 280" stroke="#e74c3c" stroke-width="2" fill="none" marker-end="url(#arrowhead-red)"/>
                        
                        <text x="90" y="310" text-anchor="middle" font-family="Arial" font-size="13" fill="#e74c3c">restarting</text>
                        <text x="290" y="310" text-anchor="middle" font-family="Arial" font-size="13" fill="#e74c3c">restarting</text>
                        <text x="390" y="340" text-anchor="middle" font-family="Arial" font-size="13" fill="#e74c3c">restarting</text>
                        
                        <!-- Arrow markers -->
                        <defs>
                            <marker id="arrowhead" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                                <polygon points="0 0, 8 3, 0 6" fill="#333"/>
                            </marker>
                            <marker id="arrowhead-red" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                                <polygon points="0 0, 8 3, 0 6" fill="#e74c3c"/>
                            </marker>
                        </defs>
                    </svg>
                    <div style="margin-top: 15px; font-size: 1em; color: #7f8c8d;">
                        <strong>Key Innovation:</strong> Dynamic coordination without deadlocks
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 8: Tech Deep Dive - Backend Abstraction Challenges -->
        <div class="slide">
            <div class="icon">🔧</div>
            <h2>Tech Deep Dive: Backend Abstraction Challenges</h2>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 40px; align-items: center;">
                <div>
                    <ul style="font-size: 1.4em;">
                        <li><strong>🎭 Unified Interface:</strong> One protocol for 8+ backends</li>
                        <li><strong>🛠️ Tool Integration:</strong> Search, code, MCP support</li>
                        <li><strong>⚙️ Format Normalization:</strong> Common response protocol</li>
                        <li><strong>🔀 Provider Workarounds:</strong> Handle unique limitations</li>
                    </ul>
                </div>
                <div>
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 15px;">
                        <div style="font-size: 1.1em; font-weight: bold; margin-bottom: 15px; color: #2c3e50;">Backend Challenges:</div>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; font-size: 0.9em;">
                            <div style="background: #fff3cd; padding: 10px; border-radius: 8px; text-align: center;">
                                <div style="font-weight: bold; color: #f39c12;">Claude Code CLI</div>
                                <div style="font-size: 0.8em; color: #666;">Context sharing across agents</div>
                            </div>
                            <div style="background: #ffebee; padding: 10px; border-radius: 8px; text-align: center;">
                                <div style="font-weight: bold; color: #e74c3c;">Gemini API</div>
                                <div style="font-size: 0.8em; color: #666;">Can't mix builtin + custom tools</div>
                            </div>
                            <div style="background: #e1f5fe; padding: 10px; border-radius: 8px; text-align: center;">
                                <div style="font-weight: bold; color: #0288d1;">GPT-5</div>
                                <div style="font-size: 0.8em; color: #666;">API change (reasoning, streaming etc.)</div>
                            </div>
                            <div style="background: #f3e5f5; padding: 10px; border-radius: 8px; text-align: center;">
                                <div style="font-weight: bold; color: #7b1fa2;">Most Backends</div>
                                <div style="font-size: 0.8em; color: #666;">Unable to autonomously collaborate</div>
                            </div>
                        </div>
                        <div style="margin-top: 15px; background: #e8f5e8; padding: 10px; border-radius: 8px; text-align: center;">
                            <div style="font-weight: bold; color: #27ae60;">🎯 Our Solution:</div>
                            <div style="font-size: 0.9em; color: #2d5a3d;">Binary Decision Framework (Explained in the next slide)</div>
                        </div>
                    </div>
                    <div style="margin-top: 15px; font-size: 1em; color: #7f8c8d; text-align: center;">
                        <strong>Result:</strong> Each backend ~200-300 lines with unique workarounds
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 9: Tech Deep Dive - Binary Decision Framework Solution -->
        <div class="slide">
            <div class="icon">🎯</div>
            <h2>Tech Deep Dive: Binary Decision Framework Solution</h2>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 40px; align-items: center;">
                <div>
                    <ul style="font-size: 1.4em;">
                        <li><strong>⚖️ Binary Choice:</strong> Each agent must choose: vote OR new_answer</li>
                        <li><strong>💥 Vote Invalidation:</strong> Any new_answer invalidates ALL existing votes</li>
                        <li><strong>🔄 Reset & Restart:</strong> All agents restart with updated answer context</li>
                        <li><strong>🎭 Anonymous Voting:</strong> Agents see "agent1", "agent2" etc.</li>
                    </ul>
                </div>
                <div style="text-align: center;">
                    <svg viewBox="0 0 500 380" xmlns="http://www.w3.org/2000/svg" style="width: 100%; max-width: 450px; height: auto;">
                        <!-- Round 1 -->
                        <text x="250" y="25" text-anchor="middle" font-family="Arial" font-size="15" font-weight="bold" fill="#2c3e50">Round 1: Agents 1,3,4 vote for Agent 4</text>
                        
                        <!-- Agents with votes -->
                        <rect x="50" y="45" width="80" height="25" rx="3" fill="#00b894" stroke="#00cec9" stroke-width="1"/>
                        <text x="90" y="62" text-anchor="middle" font-family="Arial" font-size="12" fill="white">Vote: agent4</text>
                        
                        <rect x="140" y="45" width="80" height="25" rx="3" fill="#ddd" stroke="#bbb" stroke-width="1"/>
                        <text x="180" y="62" text-anchor="middle" font-family="Arial" font-size="12" fill="#666">No vote yet</text>
                        
                        <rect x="230" y="45" width="80" height="25" rx="3" fill="#00b894" stroke="#00cec9" stroke-width="1"/>
                        <text x="270" y="62" text-anchor="middle" font-family="Arial" font-size="12" fill="white">Vote: agent4</text>
                        
                        <rect x="320" y="45" width="80" height="25" rx="3" fill="#a29bfe" stroke="#6c5ce7" stroke-width="1"/>
                        <text x="360" y="62" text-anchor="middle" font-family="Arial" font-size="12" fill="white">Answer+Vote</text>
                        
                        <!-- Disruption -->
                        <text x="250" y="105" text-anchor="middle" font-family="Arial" font-size="14" font-weight="bold" fill="#e74c3c">⚡ Agent 2 provides new_answer</text>
                        
                        <!-- Reset arrows -->
                        <path d="M 90 75 L 90 90" stroke="#e74c3c" stroke-width="2" marker-end="url(#x-mark)"/>
                        <path d="M 270 75 L 270 90" stroke="#e74c3c" stroke-width="2" marker-end="url(#x-mark)"/>
                        <path d="M 360 75 L 360 90" stroke="#e74c3c" stroke-width="2" marker-end="url(#x-mark)"/>
                        
                        <!-- Reset mechanism -->
                        <rect x="140" y="120" width="80" height="25" rx="3" fill="#fd79a8" stroke="#e84393" stroke-width="1"/>
                        <text x="180" y="137" text-anchor="middle" font-family="Arial" font-size="12" fill="white">new_answer</text>
                        
                        <!-- Round 2 -->
                        <text x="250" y="175" text-anchor="middle" font-family="Arial" font-size="15" font-weight="bold" fill="#2c3e50">Round 2: All agents restart with 2 answers</text>
                        
                        <!-- New state -->
                        <rect x="50" y="195" width="80" height="25" rx="3" fill="#74b9ff" stroke="#0984e3" stroke-width="1"/>
                        <text x="90" y="212" text-anchor="middle" font-family="Arial" font-size="11" fill="white">restart_pending</text>
                        
                        <rect x="140" y="195" width="80" height="25" rx="3" fill="#a29bfe" stroke="#6c5ce7" stroke-width="1"/>
                        <text x="180" y="212" text-anchor="middle" font-family="Arial" font-size="11" fill="white">Has new answer</text>
                        
                        <rect x="230" y="195" width="80" height="25" rx="3" fill="#74b9ff" stroke="#0984e3" stroke-width="1"/>
                        <text x="270" y="212" text-anchor="middle" font-family="Arial" font-size="11" fill="white">restart_pending</text>
                        
                        <rect x="320" y="195" width="80" height="25" rx="3" fill="#a29bfe" stroke="#6c5ce7" stroke-width="1"/>
                        <text x="360" y="212" text-anchor="middle" font-family="Arial" font-size="11" fill="white">Has old answer</text>
                        
                        <!-- Binary decision -->
                        <text x="250" y="250" text-anchor="middle" font-family="Arial" font-size="13" font-weight="bold" fill="#2c3e50">Each agent decides: vote OR new_answer</text>
                        
                        <!-- Key insight -->
                        <rect x="70" y="265" width="360" height="28" rx="5" fill="#fff3cd" stroke="#f39c12" stroke-width="2"/>
                        <text x="250" y="283" text-anchor="middle" font-family="Arial" font-size="13" font-weight="bold" fill="#856404">🔑 Any new_answer resets ALL votes</text>
                        
                        <!-- Vote invalidation note -->
                        <text x="250" y="315" text-anchor="middle" font-family="Arial" font-size="12" fill="#e74c3c">Votes from Round 1: ❌ INVALID</text>
                        <text x="250" y="335" text-anchor="middle" font-family="Arial" font-size="12" fill="#27ae60">New decisions needed based on 2 available answers</text>
                        
                        <!-- Markers -->
                        <defs>
                            <marker id="x-mark" markerWidth="10" markerHeight="10" refX="5" refY="5" orient="auto">
                                <path d="M 2 2 L 8 8 M 8 2 L 2 8" stroke="#e74c3c" stroke-width="2"/>
                            </marker>
                        </defs>
                    </svg>
                    <div style="margin-top: 15px; font-size: 1em; color: #7f8c8d;">
                        <strong>Key Innovation:</strong> Dynamic equilibrium through vote invalidation
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 10: Evolution from v0.0.1 to v0.0.9 -->
        <div class="slide">
            <div class="icon">🚀</div>
            <h2>MassGen Evolution: v0.0.1 → v0.0.9</h2>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 50px; margin: 50px 0; align-items: center; justify-items: center;">
                <div style="background: linear-gradient(135deg, #74b9ff, #0984e3); color: white; padding: 40px; border-radius: 25px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); max-width: 350px; text-align: center;">
                    <div style="font-size: 3em; margin-bottom: 20px;">🏗️</div>
                    <h3 style="margin: 0 0 15px 0; font-size: 1.6em;">Foundation Era</h3>
                    <div style="font-size: 1.1em; opacity: 0.9; margin-bottom: 25px;">v0.0.1 - v0.0.3</div>
                    <div style="font-size: 1.1em; line-height: 1.6;">
                        Core framework, basic streaming,<br>
                        Claude, Gemini, GPT/o, Grok
                    </div>
                </div>
                <div style="background: linear-gradient(135deg, #e74c3c, #c0392b); color: white; padding: 40px; border-radius: 25px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); max-width: 400px; text-align: center; position: relative;">
                    <div style="position: absolute; top: -15px; right: -15px; background: #f39c12; color: white; padding: 8px 20px; border-radius: 25px; font-size: 0.9em; font-weight: bold; box-shadow: 0 4px 15px rgba(0,0,0,0.3);">LATEST</div>
                    <div style="font-size: 3em; margin-bottom: 20px;">🎯</div>
                    <h3 style="margin: 0 0 15px 0; font-size: 1.6em;">Expansion Era</h3>
                    <div style="font-size: 1.1em; opacity: 0.9; margin-bottom: 25px;">v0.0.4 - v0.0.9</div>
                    <div style="font-size: 1.1em; line-height: 1.6;">
                        Claude Code, GPT-5, MCP<br>
                        GPT-OSS, Local models, 8+ providers
                    </div>
                </div>
            </div>
            <div style="text-align: center; margin-top: 40px;">
                <div style="display: inline-flex; gap: 40px; align-items: center; font-size: 1.3em; color: #2c3e50;">
                    <div><strong style="color: #e74c3c;">8</strong> Major Releases</div>
                    <div style="color: #bdc3c7;">•</div>
                    <div><strong style="color: #e74c3c;">150+</strong> Commits</div>
                    <div style="color: #bdc3c7;">•</div>
                    <div><strong style="color: #e74c3c;">14 Days</strong> Foundation→Expansion</div>
                </div>
            </div>
        </div>

        <!-- Slide 11: Live Demo Examples -->
        <div class="slide">
            <div class="icon">🎬</div>
            <h2>Live Demonstrations</h2>
            <div class="demo-grid">
                <div class="demo-box">
                    <strong>🏆 IMO 2025 Winner Research:</strong> Multi-agent fact-checking → unanimous consensus on Google DeepMind victory
                    <div style="margin-top: 10px; font-size: 0.9em; opacity: 0.9;">
                        <strong>Result:</strong> Accurate identification despite conflicting information
                    </div>
                </div>
                <div class="demo-box">
                    <strong>📝 Creative Writing:</strong> Robot-music story refined through collaborative voting → final unanimous approval
                    <div style="margin-top: 10px; font-size: 0.9em; opacity: 0.9;">
                        <strong>Result:</strong> Higher quality narrative through collaborative editing
                    </div>
                </div>
                <div class="demo-box">
                    <strong>🌍 Stockholm Travel Guide:</strong> Agents combined local knowledge + real-time data → comprehensive October 2025 plan
                    <div style="margin-top: 10px; font-size: 0.9em; opacity: 0.9;">
                        <strong>Result:</strong> Detailed recommendations no single agent could provide
                    </div>
                </div>
                <div class="demo-box">
                    <strong>💰 Technical Analysis:</strong> Complex Grok-4 HLE pricing calculation through iterative refinement
                    <div style="margin-top: 10px; font-size: 0.9em; opacity: 0.9;">
                        <strong>Result:</strong> Accurate cost estimates through collaborative validation
                    </div>
                </div>
            </div>
            <div style="text-align: center; margin-top: 30px;">
                <a href="https://case.massgen.ai" style="color: #3498db; font-size: 1.4em; text-decoration: underline; font-weight: bold;">📚 case.massgen.ai - Complete Case Studies</a>
            </div>
        </div>

        <!-- Slide 12: Getting Started -->
        <div class="slide">
            <div class="icon">⚡</div>
            <h2>Get Started in 60 Seconds</h2>
            <div class="code-snippet">
                # 1. Clone and setup<br>
                git clone https://github.com/Leezekun/MassGen<br>
                cd MassGen && pip install uv && uv venv<br><br>
                
                # 2. Configure API keys<br>
                cp .env.example .env  # Add your API keys<br><br>
                
                # 3. Run single agent (quick test)<br>
                uv run python -m massgen.cli --model gemini-2.5-flash "When is your knowledge up to"<br><br>
                
                # 4. Run multi-agent collaboration<br>
                uv run python -m massgen.cli --config three_agents_default.yaml "Summarize latest news of github.com/Leezekun/MassGen"
            </div>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-top: 30px;">
                <div style="text-align: center; padding: 20px; background: rgba(46, 204, 113, 0.1); border-radius: 15px;">
                    <h3 style="color: #27ae60; margin-bottom: 15px;">✅ Supported Models & Providers</h3>
                    <div style="font-size: 1em; line-height: 1.5;">
                        <div style="font-weight: bold; margin-bottom: 8px;">🏢 Major Providers:</div>
                        <div>Anthropic Claude & Claude Code • Google Gemini • OpenAI GPT • xAI Grok • ZAI GLM</div>
                        <div style="font-weight: bold; margin: 12px 0 8px 0;">🏠 Local & Extended:</div>
                        <div>Cerebras • Fireworks • Groq • LM Studio • OpenRouter • Together...</div>
                    </div>
                </div>
                <div style="text-align: center; padding: 20px; background: rgba(52, 152, 219, 0.1); border-radius: 15px;">
                    <h3 style="color: #3498db; margin-bottom: 15px;">🛠️ Advanced Tools</h3>
                    <div style="font-size: 1.1em;">Web Search, Code Execution, File Operations, MCP</div>
                </div>
            </div>
        </div>

        <!-- Slide 13: Roadmap & Vision -->
        <div class="slide">
            <div class="icon">🔮</div>
            <h2>Vision: The Path to Exponential Intelligence</h2>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 40px; align-items: center;">
                <div>
                    <ul style="font-size: 1.6em;">
                        <li><strong>Hurdles:</strong> Shared memory, context, interoperability</li>
                        <li><strong>Roadmap:</strong> More models/agents, web UI</li>
                        <li><strong>Vision:</strong> Recursive agents bootstrapping intelligence</li>
                    </ul>
                </div>
                <div style="text-align: center;">
                    <svg viewBox="0 0 1200 600" xmlns="http://www.w3.org/2000/svg" style="width: 100%; max-width: 500px; height: auto;">
                      <defs>
                        <marker id="arrowhead" markerWidth="8" markerHeight="6" 
                         refX="7" refY="3" orient="auto">
                          <polygon points="0 0, 8 3, 0 6" fill="#333" />
                        </marker>
                        
                        <!-- Gradient definitions -->
                        <linearGradient id="grokGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                          <stop offset="0%" style="stop-color:#4CAF50;stop-opacity:1" />
                          <stop offset="100%" style="stop-color:#2E7D32;stop-opacity:1" />
                        </linearGradient>
                        
                        <linearGradient id="geminiGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                          <stop offset="0%" style="stop-color:#2196F3;stop-opacity:1" />
                          <stop offset="100%" style="stop-color:#1565C0;stop-opacity:1" />
                        </linearGradient>
                        
                        <linearGradient id="claudeGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                          <stop offset="0%" style="stop-color:#FF9800;stop-opacity:1" />
                          <stop offset="100%" style="stop-color:#E65100;stop-opacity:1" />
                        </linearGradient>
                        
                        <linearGradient id="massgenGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                          <stop offset="0%" style="stop-color:#9C27B0;stop-opacity:1" />
                          <stop offset="100%" style="stop-color:#4A148C;stop-opacity:1" />
                        </linearGradient>
                      </defs>
                      
                      <!-- Background -->
                      <rect width="1200" height="600" fill="#f8f9fa"/>
                      
                      <!-- Level 1: Individual Agents -->
                      <text x="100" y="100" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#666">
                        Agents
                      </text>
                      
                      <!-- Agent clusters -->
                      <!-- Grok -->
                      <circle cx="140" cy="130" r="20" fill="url(#grokGrad)" stroke="#2E7D32" stroke-width="3"/>
                      <circle cx="180" cy="130" r="20" fill="url(#grokGrad)" stroke="#2E7D32" stroke-width="3"/>
                      <circle cx="220" cy="130" r="20" fill="url(#grokGrad)" stroke-width="3"/>
                      <text x="180" y="170" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" fill="#333" font-weight="bold">Grok</text>
                      
                      <!-- Gemini -->
                      <circle cx="280" cy="130" r="20" fill="url(#geminiGrad)" stroke="#1565C0" stroke-width="3"/>
                      <circle cx="320" cy="130" r="20" fill="url(#geminiGrad)" stroke="#1565C0" stroke-width="3"/>
                      <circle cx="360" cy="130" r="20" fill="url(#geminiGrad)" stroke-width="3"/>
                      <text x="320" y="170" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" fill="#333" font-weight="bold">Gemini</text>
                      
                      <!-- Claude -->
                      <circle cx="420" cy="130" r="20" fill="url(#claudeGrad)" stroke="#E65100" stroke-width="3"/>
                      <circle cx="460" cy="130" r="20" fill="url(#claudeGrad)" stroke="#E65100" stroke-width="3"/>
                      <circle cx="500" cy="130" r="20" fill="url(#claudeGrad)" stroke-width="3"/>
                      <text x="460" y="170" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" fill="#333" font-weight="bold">Claude</text>
                      
                      <!-- GPT -->
                      <circle cx="560" cy="130" r="20" fill="#10B981" stroke="#047857" stroke-width="3"/>
                      <circle cx="600" cy="130" r="20" fill="#10B981" stroke="#047857" stroke-width="3"/>
                      <circle cx="640" cy="130" r="20" fill="#10B981" stroke-width="3"/>
                      <text x="600" y="170" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" fill="#333" font-weight="bold">GPT</text>
                      
                      <!-- AG2 -->
                      <circle cx="700" cy="130" r="20" fill="#DC2626" stroke="#991B1B" stroke-width="3"/>
                      <circle cx="740" cy="130" r="20" fill="#DC2626" stroke="#991B1B" stroke-width="3"/>
                      <circle cx="780" cy="130" r="20" fill="#DC2626" stroke-width="3"/>
                      <text x="740" y="170" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" fill="#333" font-weight="bold">AG2</text>
                      
                      <!-- Level 2: Systems -->
                      <text x="50" y="220" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#666">
                        Systems
                      </text>
                      
                      <!-- System boxes -->
                      <rect x="60" y="250" width="140" height="70" rx="12" fill="url(#grokGrad)" stroke="#2E7D32" stroke-width="3"/>
                      <text x="130" y="295" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="white">
                        Grok Heavy
                      </text>
                      
                      <rect x="220" y="250" width="140" height="70" rx="12" fill="url(#geminiGrad)" stroke="#1565C0" stroke-width="3"/>
                      <text x="290" y="295" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="white">
                        DeepThink
                      </text>
                      
                      <rect x="380" y="250" width="140" height="70" rx="12" fill="url(#claudeGrad)" stroke="#E65100" stroke-width="3"/>
                      <text x="450" y="295" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="white">
                        Claude Code
                      </text>
                      
                      <rect x="540" y="250" width="140" height="70" rx="12" fill="#10B981" stroke="#047857" stroke-width="3"/>
                      <text x="610" y="295" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="white">
                        ChatGPT
                      </text>
                      
                      <rect x="700" y="250" width="140" height="70" rx="12" fill="#DC2626" stroke="#991B1B" stroke-width="3"/>
                      <text x="770" y="295" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="white">
                        AG2
                      </text>
                      
                      <!-- Arrows from agents to systems -->
                      <line x1="180" y1="190" x2="130" y2="240" stroke="#333" stroke-width="4" marker-end="url(#arrowhead)" opacity="0.7"/>
                      <line x1="320" y1="190" x2="290" y2="240" stroke="#333" stroke-width="4" marker-end="url(#arrowhead)" opacity="0.7"/>
                      <line x1="460" y1="190" x2="450" y2="240" stroke="#333" stroke-width="4" marker-end="url(#arrowhead)" opacity="0.7"/>
                      <line x1="600" y1="190" x2="610" y2="240" stroke="#333" stroke-width="4" marker-end="url(#arrowhead)" opacity="0.7"/>
                      <line x1="740" y1="190" x2="770" y2="240" stroke="#333" stroke-width="4" marker-end="url(#arrowhead)" opacity="0.7"/>
                      
                      <!-- Level 3: MassGen -->
                      <text x="100" y="400" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#666">
                        Orchestrator
                      </text>
                      
                      <!-- MassGen -->
                      <rect x="200" y="430" width="500" height="90" rx="20" fill="url(#massgenGrad)" stroke="#4A148C" stroke-width="5"/>
                      <text x="450" y="485" text-anchor="middle" font-family="Arial, sans-serif" font-size="32" font-weight="bold" fill="white">
                        MassGen
                      </text>
                      
                      <!-- Arrows from systems to MassGen -->
                      <line x1="130" y1="330" x2="300" y2="420" stroke="#333" stroke-width="5" marker-end="url(#arrowhead)"/>
                      <line x1="290" y1="330" x2="380" y2="420" stroke="#333" stroke-width="5" marker-end="url(#arrowhead)"/>
                      <line x1="450" y1="330" x2="450" y2="420" stroke="#333" stroke-width="5" marker-end="url(#arrowhead)"/>
                      <line x1="610" y1="330" x2="520" y2="420" stroke="#333" stroke-width="5" marker-end="url(#arrowhead)"/>
                      <line x1="770" y1="330" x2="600" y2="420" stroke="#333" stroke-width="5" marker-end="url(#arrowhead)"/>
                      
                      <!-- Scaling visualization -->
                      <g transform="translate(1050, 120)">
                        <circle cx="0" cy="0" r="30" fill="#E3F2FD" stroke="#1976D2" stroke-width="4"/>
                        <text x="0" y="10" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" fill="#1976D2" font-weight="bold">1×</text>
                      </g>
                      
                      <g transform="translate(1050, 220)">
                        <circle cx="0" cy="0" r="40" fill="#E8F5E8" stroke="#4CAF50" stroke-width="4"/>
                        <text x="0" y="10" text-anchor="middle" font-family="Arial, sans-serif" font-size="26" fill="#4CAF50" font-weight="bold">10×</text>
                      </g>
                      
                      <g transform="translate(1050, 340)">
                        <circle cx="0" cy="0" r="50" fill="#F3E5F5" stroke="#9C27B0" stroke-width="4"/>
                        <text x="0" y="10" text-anchor="middle" font-family="Arial, sans-serif" font-size="28" fill="#9C27B0" font-weight="bold">100×</text>
                      </g>
                      
                      <!-- Scaling arrows -->
                      <line x1="1050" y1="155" x2="1050" y2="175" stroke="#666" stroke-width="3" marker-end="url(#arrowhead)"/>
                      <line x1="1050" y1="270" x2="1050" y2="290" stroke="#666" stroke-width="3" marker-end="url(#arrowhead)"/>
                      
                      <!-- Challenge callout -->
                      <g transform="translate(820, 430)">
                        <rect x="0" y="0" width="300" height="120" rx="15" fill="#FFEBEE" stroke="#F44336" stroke-width="4"/>
                        <text x="150" y="35" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#C62828">
                          Challenges
                        </text>
                        <text x="150" y="70" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" fill="#333">
                          Consensus
                        </text>
                        <text x="150" y="100" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" fill="#333">
                          Shared Context
                        </text>
                      </g>
                    </svg>
                    <div style="margin-top: 15px; font-size: 1em; color: #7f8c8d; font-style: italic;">
                        The Path to Exponential Intelligence
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 14: Call to Action -->
        <div class="slide" style="background: linear-gradient(135deg, #667eea, #764ba2), url('https://github.com/Leezekun/MassGen/blob/main/assets/cos.png?raw=true'); background-size: cover; background-blend-mode: overlay; background-position: center;">
            <div class="icon">🚀</div>
            <h2 style="color: white; text-shadow: 2px 2px 4px rgba(0,0,0,0.7);">Join the Multi-Agent Revolution</h2>
            <div class="call-to-action" style="background: rgba(255,255,255,0.15); backdrop-filter: blur(10px); border: 1px solid rgba(255,255,255,0.2);">
                <div style="font-size: 1.4em; margin-bottom: 20px; color: white; text-shadow: 1px 1px 2px rgba(0,0,0,0.7);">
                    <strong>Build Scalable, Collaborative AI Systems</strong>
                </div>
                <div class="cta-links">
                    <a href="https://github.com/Leezekun/MassGen" class="cta-link" style="background: rgba(255,255,255,0.35); backdrop-filter: blur(5px); border: 1px solid rgba(255,255,255,0.5);">⭐ Star on GitHub</a>
                    <a href="https://discord.massgen.ai" class="cta-link" style="background: rgba(255,255,255,0.35); backdrop-filter: blur(5px); border: 1px solid rgba(255,255,255,0.5);">💬 Join Discord</a>
                    <a href="https://massgen.ai" class="cta-link" style="background: rgba(255,255,255,0.35); backdrop-filter: blur(5px); border: 1px solid rgba(255,255,255,0.5);">🌐 Visit Website</a>
                    <a href="https://case.massgen.ai" class="cta-link" style="background: rgba(255,255,255,0.35); backdrop-filter: blur(5px); border: 1px solid rgba(255,255,255,0.5);">📚 Case Studies</a>
                </div>
                <div style="margin-top: 30px;">
                    <div class="version-badge" style="background: linear-gradient(135deg, #e74c3c, #c0392b); backdrop-filter: blur(5px); border: 1px solid rgba(255,255,255,0.3);">
                        🚀 v0.0.3→v0.0.9 Evolution | Claude Code + MCP Integration + GPT-5 + Local Models
                    </div>
                </div>
            </div>
            <div style="text-align: center; margin-top: 30px; font-size: 1.2em; color: white; text-shadow: 2px 2px 4px rgba(0,0,0,0.7);">
                <strong>Thank you DataHack Summit 2025!</strong><br>
                <span style="color: #e8f4fd; text-shadow: 1px 1px 2px rgba(0,0,0,0.5);">Questions & Discussion</span>
            </div>
        </div>
    </div>

    <!-- Navigation -->
    <div class="slide-counter">
        <span id="current-slide">1</span> / <span id="total-slides">14</span>
        <div class="slide-title" id="slide-title">Title - DataHack Summit 2025</div>
    </div>

    <div class="slide-nav">
        <div class="nav-grid" id="nav-grid">
            <!-- Dots will be generated by JavaScript -->
        </div>
    </div>

    <div class="navigation">
        <button class="nav-btn" id="prev-btn" onclick="changeSlide(-1)">← Previous</button>
        <button class="nav-btn" id="next-btn" onclick="changeSlide(1)">Next →</button>
    </div>

    <script>
        let currentSlide = 0;
        const slides = document.querySelectorAll('.slide');
        const totalSlides = slides.length;

        // Slide titles for navigation
        const slideTitles = [
            "Title - DataHack Summit 2025",
            "Problem - Single-Agent Limitation", 
            "Solution - Multi-Agent Collaboration",
            "Evidence - Performance Gains",
            "Architecture - System Design",
            "Features & Capabilities",
            "Tech - Async Streaming",
            "Tech - Backend Challenges", 
            "Tech - Binary Decision Framework",
            "Evolution - v0.0.1 to v0.0.9",
            "Demo - Live Examples",
            "Getting Started - 60 Seconds",
            "Vision - Exponential Intelligence",
            "Call to Action - Join Revolution"
        ];

        document.getElementById('total-slides').textContent = totalSlides;

        // Generate navigation dots
        const navGrid = document.getElementById('nav-grid');
        for (let i = 0; i < totalSlides; i++) {
            const dot = document.createElement('div');
            dot.className = 'nav-dot';
            dot.title = `Slide ${i + 1}: ${slideTitles[i]}`;
            dot.onclick = () => showSlide(i);
            navGrid.appendChild(dot);
        }

        function showSlide(n) {
            slides[currentSlide].classList.remove('active');
            currentSlide = (n + totalSlides) % totalSlides;
            slides[currentSlide].classList.add('active');
            
            document.getElementById('current-slide').textContent = currentSlide + 1;
            document.getElementById('slide-title').textContent = slideTitles[currentSlide];
            
            // Update navigation dots
            document.querySelectorAll('.nav-dot').forEach((dot, index) => {
                dot.classList.toggle('active', index === currentSlide);
            });
            
            // Update navigation buttons
            document.getElementById('prev-btn').disabled = currentSlide === 0;
            document.getElementById('next-btn').disabled = currentSlide === totalSlides - 1;
        }

        function changeSlide(direction) {
            if (direction === 1 && currentSlide < totalSlides - 1) {
                showSlide(currentSlide + 1);
            } else if (direction === -1 && currentSlide > 0) {
                showSlide(currentSlide - 1);
            }
        }

        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (e.key === 'ArrowRight' || e.key === ' ') {
                e.preventDefault();
                changeSlide(1);
            } else if (e.key === 'ArrowLeft') {
                e.preventDefault();
                changeSlide(-1);
            } else if (e.key >= '1' && e.key <= '9') {
                e.preventDefault();
                const slideNum = parseInt(e.key) - 1;
                if (slideNum < totalSlides) {
                    showSlide(slideNum);
                }
            }
        });

        // Initialize
        showSlide(0);
    </script>
</body>
</html>