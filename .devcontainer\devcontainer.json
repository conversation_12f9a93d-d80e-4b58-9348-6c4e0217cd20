{"name": "MassGen Development Environment", "image": "mcr.microsoft.com/devcontainers/python:1-3.12-bookworm", "features": {"ghcr.io/devcontainers/features/git:1": {}, "ghcr.io/devcontainers/features/github-cli:1": {}}, "customizations": {"vscode": {"extensions": ["ms-python.python", "ms-python.black-formatter", "ms-python.isort", "ms-python.flake8", "ms-toolsai.jupyter", "redhat.vscode-yaml"], "settings": {"python.defaultInterpreterPath": "/usr/local/bin/python", "python.formatting.provider": "black", "python.linting.enabled": true, "python.linting.flake8Enabled": true, "editor.formatOnSave": true}}}, "postCreateCommand": ".devcontainer/post-create.sh", "forwardPorts": [8000, 8080], "remoteUser": "vscode", "containerEnv": {"PYTHONPATH": "/workspaces/MassGen"}}