from __future__ import annotations

"""
Claude backend implementation using Anthropic's Messages API.
Production-ready implementation with full multi-tool support.

✅ FEATURES IMPLEMENTED:
- ✅ Messages API integration with streaming support
- ✅ Multi-tool support (server-side + user-defined tools combined)
- ✅ Web search tool integration with pricing tracking
- ✅ Code execution tool integration with session management
- ✅ Tool message format conversion for MassGen compatibility
- ✅ Advanced streaming with tool parameter streaming
- ✅ Error handling and token usage tracking
- ✅ Production-ready pricing calculations (2025 rates)

Multi-Tool Capabilities:
- Can combine web search + code execution + user functions in single request
- No API limitations unlike other providers
- Parallel and sequential tool execution supported
- Perfect integration with MassGen StreamChunk pattern
"""

import os
import json
import logging
import asyncio
from typing import Dict, List, Any, AsyncGenerator, Optional, Literal
from .base import LLMBackend, StreamChunk


# Set up logger
logger = logging.getLogger(__name__)

# MCP integration imports
try:
    from ..mcp_tools import MultiMCPClient, MCPError, MCPConnectionError
    from ..mcp_tools.config_validator import MCPConfigValidator
    from ..mcp_tools.circuit_breaker import MCPCircuitBreaker
    from ..mcp_tools.exceptions import (
        MCPConfigurationError,
        MCPValidationError,
        MCPTimeoutError,
    )
    from ..mcp_tools.security import validate_url
except ImportError as e:  # MCP not installed or import failed within mcp_tools
    logger.debug(f"MCP import failed: {e}")
    MultiMCPClient = None  # type: ignore[assignment]
    MCPError = ImportError  # type: ignore[assignment]
    MCPConnectionError = ImportError  # type: ignore[assignment]
    MCPConfigValidator = None  # type: ignore[assignment]
    MCPCircuitBreaker = None  # type: ignore[assignment]
    MCPConfigurationError = ImportError  # type: ignore[assignment]
    MCPValidationError = ImportError  # type: ignore[assignment]
    MCPTimeoutError = ImportError  # type: ignore[assignment]
    MCPServerError = ImportError  # type: ignore[assignment]
    validate_url = None


# Import common utilities
from .common import Function


class ClaudeBackend(LLMBackend):
    """Claude backend using Anthropic's Messages API with full multi-tool support."""

    def __init__(self, api_key: Optional[str] = None, **kwargs):
        # Extract MCP-specific kwargs before calling super().__init__
        init_kwargs = dict(kwargs)
        self.mcp_servers = init_kwargs.pop("mcp_servers", [])
        self.allowed_tools = init_kwargs.pop("allowed_tools", None)
        self.exclude_tools = init_kwargs.pop("exclude_tools", None)
        self._max_mcp_message_history = init_kwargs.pop("max_mcp_message_history", 200)

        super().__init__(api_key, **init_kwargs)
        self.api_key = api_key or os.getenv("ANTHROPIC_API_KEY")
        self.search_count = 0  # Track web search usage for pricing
        self.code_session_hours = 0.0  # Track code execution usage

        # Circuit breakers for different transport types with explicit configuration
        self._mcp_tools_circuit_breaker = None  # For stdio + streamable-http
        self._http_circuit_breaker = None
        self._circuit_breakers_enabled = MCPCircuitBreaker is not None
        
        # Initialize circuit breakers if available
        if self._circuit_breakers_enabled:
            from ..mcp_tools.circuit_breaker import CircuitBreakerConfig
            
            # Configure circuit breakers with explicit parameters for different transport types
            mcp_tools_config = CircuitBreakerConfig(
                max_failures=3,        
                reset_time_seconds=30,  
                backoff_multiplier=2,   
                max_backoff_multiplier=8 
            )
            self._mcp_tools_circuit_breaker = MCPCircuitBreaker(mcp_tools_config)
            
            http_config = CircuitBreakerConfig(
                max_failures=5,         
                reset_time_seconds=60,
                backoff_multiplier=2,   
                max_backoff_multiplier=16  
            )
            self._http_circuit_breaker = MCPCircuitBreaker(http_config)
            
            logger.debug("Circuit breakers initialized for MCP transport types")
        else:
            logger.debug("Circuit breakers not available - proceeding without circuit breaker protection")

        # Transport Types:
        # - "stdio" & "streamable-http"
        # - "http": Use Claude's native MCP client
        self._mcp_tools_servers: List[Dict[str, Any]] = []    # stdio + streamable-http servers
        self._http_servers: List[Dict[str, Any]] = []         # Native Claude HTTP MCP servers

        # Function registry for mcp_tools-based servers
        self.functions: Dict[str, Function] = {}

        # MCP initialization state
        self._mcp_initialized = False
        self._mcp_client = None
        
        # Lock to prevent concurrent MCP initialization
        self._mcp_setup_lock = asyncio.Lock()

        # Separate MCP servers by transport type if any are configured
        if self.mcp_servers:
            self._separate_mcp_servers_by_transport_type()

    def _normalize_mcp_servers(self) -> List[Dict[str, Any]]:
        """Validate and normalize mcp_servers into a list of dicts."""
        servers = self.mcp_servers
        if not servers:
            return []
        if isinstance(servers, dict):
            return [servers]
        if not isinstance(servers, list):
            raise ValueError(
                f"mcp_servers must be a list or dict, got {type(servers).__name__}"
            )
        normalized: List[Dict[str, Any]] = []
        for idx, entry in enumerate(servers):
            if not isinstance(entry, dict):
                raise ValueError(
                    f"MCP server configuration at index {idx} must be a dictionary, got {type(entry).__name__}"
                )
            normalized.append(entry)
        return normalized

    def _separate_mcp_servers_by_transport_type(self) -> None:
        """
        Separate MCP servers into different transport types for Claude.
        
        Transport Types:
        - "stdio" & "streamable-http"
        """
        validated_servers = self._normalize_mcp_servers()

        for server in validated_servers:
            # Accept both 'type'
            transport_type = server.get("type")
            server_name = server.get("name", "unnamed")

            if not transport_type:
                logger.warning(
                    f"MCP server '{server_name}' missing required 'type' field. "
                    f"Supported types: 'stdio', 'streamable-http', 'http'. Skipping server."
                )
                continue

            if transport_type in ["stdio", "streamable-http"]:
                # Both stdio and streamable-http
                self._mcp_tools_servers.append(server)
            elif transport_type == "http":
                # HTTP servers use Claude's native MCP client
                self._http_servers.append(server)
            else:
                logger.warning(
                    f"Unknown MCP transport type '{transport_type}' for server '{server_name}'. "
                    f"Supported types: 'stdio', 'streamable-http', 'http'. Skipping server."
                )

    async def _setup_mcp_tools(self) -> None:
        """Initialize MCP client for stdio and streamable-http servers."""
        if not self._mcp_tools_servers or self._mcp_initialized:
            return

        if MultiMCPClient is None:
            reason = "MCP import failed - MultiMCPClient not available"
            logger.warning(
                "MCP support import failed (%s). mcp_tools servers were provided; falling back to workflow tools without MCP. Ensure the 'mcp' package is installed and compatible with this codebase.",
                reason,
            )
            # Clear mcp_tools servers to prevent further attempts
            self._mcp_tools_servers = []
            return

        try:
            # Extract tool filtering parameters
            allowed_tools = self.allowed_tools
            exclude_tools = self.exclude_tools

            # Validate MCP configuration before initialization
            if MCPConfigValidator is not None:
                try:
                    backend_config = {
                        "mcp_servers": self._mcp_tools_servers,
                        "allowed_tools": self.allowed_tools,
                        "exclude_tools": self.exclude_tools,
                    }

                    # Use the comprehensive validator class for enhanced validation
                    validator = MCPConfigValidator()
                    validated_config = validator.validate_backend_mcp_config(backend_config)

                    self._mcp_tools_servers = validated_config.get("mcp_servers", self._mcp_tools_servers)

                    # Extract validated tool filtering parameters
                    allowed_tools = validated_config.get("allowed_tools", self.allowed_tools)
                    exclude_tools = validated_config.get("exclude_tools", self.exclude_tools)

                    logger.debug(
                        f"MCP configuration validation passed for {len(self._mcp_tools_servers)} mcp_tools servers"
                    )

                except MCPConfigurationError as e:
                    logger.error(f"MCP configuration validation failed: {e.original_message}")
                    self._mcp_client = None
                    raise RuntimeError(
                        f"Invalid MCP configuration: {e.original_message}"
                    ) from e
                except MCPValidationError as e:
                    logger.error(f"MCP validation failed: {e.original_message}")
                    self._mcp_client = None
                    raise RuntimeError(f"MCP validation error: {e.original_message}") from e
                except Exception as e:
                    if isinstance(e, (ImportError, AttributeError)):
                        logger.debug(f"MCP validation not available: {e}")
                    else:
                        logger.warning(f"MCP validation error: {e}")
                        self._mcp_client = None
                        raise RuntimeError(
                            f"MCP configuration validation failed: {e}"
                        ) from e
            else:
                logger.debug("MCP validation not available, proceeding without validation")

            logger.info(
                f"Setting up MCP sessions with {len(self._mcp_tools_servers)} mcp_tools servers (stdio + streamable-http)"
            )

            # Log tool filtering if configured
            if allowed_tools:
                logger.info(f"MCP tool filtering - allowed tools: {allowed_tools}")
            if exclude_tools:
                logger.info(f"MCP tool filtering - excluding: {exclude_tools}")

            # Create MCP client connection with retry logic and circuit breaker
            max_mcp_retries = 3
            mcp_connected = False

            for retry_count in range(1, max_mcp_retries + 1):
                try:
                    if retry_count > 1:
                        logger.info(f"MCP connection retry {retry_count}/{max_mcp_retries}")
                        await asyncio.sleep(0.5 * retry_count)  # Progressive backoff

                    # Apply circuit breaker filtering for mcp_tools servers
                    filtered_mcp_tools_servers = self._apply_mcp_tools_circuit_breaker_filtering(
                        self._mcp_tools_servers
                    )

                    if not filtered_mcp_tools_servers:
                        logger.warning("All mcp_tools servers are circuit breaker blocked")
                        break

                    self._mcp_client = await MultiMCPClient.create_and_connect(
                        filtered_mcp_tools_servers,
                        timeout_seconds=30,
                        allowed_tools=allowed_tools,
                        exclude_tools=exclude_tools,
                    )

                    # Record success for circuit breaker
                    await self._record_mcp_tools_success(filtered_mcp_tools_servers)

                    mcp_connected = True
                    logger.info(f"MCP connection successful on attempt {retry_count}")
                    break

                except (MCPConnectionError, MCPTimeoutError, MCPServerError, MCPError) as e:
                    # Record failure for circuit breaker
                    await self._record_mcp_tools_failure(self._mcp_tools_servers, str(e))

                    if retry_count >= max_mcp_retries:
                        logger.warning(f"MCP connection failed after {max_mcp_retries} attempts: {e}")
                        self._mcp_client = None
                        return
                    else:
                        logger.warning(f"MCP connection attempt {retry_count} failed: {e}")

            if not mcp_connected:
                logger.info("Falling back to workflow tools after MCP connection failures")
                return

            # Register tools from the client as Functions
            for tool_name, tool in self._mcp_client.tools.items():
                try:
                    # Fix closure bug by using default parameter to capture tool_name
                    def create_tool_entrypoint(captured_tool_name: str = tool_name):
                        async def tool_entrypoint(input_str: str) -> Any:
                            try:
                                arguments = json.loads(input_str)
                            except (json.JSONDecodeError, ValueError) as e:
                                logger.error(f"Invalid JSON arguments for MCP tool {captured_tool_name}: {e}")
                                raise MCPValidationError(
                                    f"Invalid JSON arguments for tool {captured_tool_name}: {e}",
                                    field="arguments",
                                    value=input_str
                                )
                            return await self._mcp_client.call_tool(
                                captured_tool_name, arguments
                            )
                        return tool_entrypoint

                    # Create the entrypoint with captured tool name
                    entrypoint = create_tool_entrypoint()

                    # Create a Function for the tool
                    function = Function(
                        name=tool_name,
                        description=tool.description,
                        parameters=tool.inputSchema,
                        entrypoint=entrypoint,
                    )

                    # Register the Function
                    self.functions[function.name] = function
                    logger.debug(f"Function: {function.name} registered")
                except Exception as e:
                    logger.error(f"Failed to register tool {tool_name}: {e}")

            self._mcp_initialized = True
            logger.info(
                f"Successfully initialized MCP mcp_tools sessions with {len(self._mcp_client.tools)} tools converted to functions"
            )

        except Exception as e:
            # Enhanced error handling for different MCP error types
            if isinstance(e, RuntimeError) and "MCP configuration" in str(e):
                raise
            elif isinstance(e, MCPConnectionError):
                logger.error(f"MCP connection failed during setup: {e}")
                self._mcp_client = None
                raise RuntimeError(f"Failed to establish MCP connections: {e}") from e
            elif isinstance(e, MCPTimeoutError):
                logger.error(f"MCP connection timed out during setup: {e}")
                self._mcp_client = None
                raise RuntimeError(f"MCP connection timeout: {e}") from e
            elif isinstance(e, MCPServerError):
                logger.error(f"MCP server error during setup: {e}")
                self._mcp_client = None
                raise RuntimeError(f"MCP server error: {e}") from e
            elif isinstance(e, MCPError):
                logger.warning(f"MCP error during setup: {e}")
                self._mcp_client = None
                return

            else:
                logger.warning(f"Failed to setup MCP sessions: {e}")
                self._mcp_client = None

    def _apply_mcp_tools_circuit_breaker_filtering(
        self, servers: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Apply circuit breaker filtering to mcp_tools servers.

        Returns:
            List of servers that pass circuit breaker checks
        """
        if not self._circuit_breakers_enabled or not self._mcp_tools_circuit_breaker:
            logger.debug("Circuit breaker not enabled for mcp_tools servers")
            return servers

        filtered_servers = []
        skipped_servers = []

        for server in servers:
            server_name = server.get("name", "unnamed")

            try:
                should_skip = self._mcp_tools_circuit_breaker.should_skip_server(server_name)
                if not should_skip:
                    filtered_servers.append(server)
                else:
                    skipped_servers.append(server_name)
            except Exception as cb_error:
                logger.warning(f"Circuit breaker should_skip_server failed for mcp_tools server {server_name}: {cb_error}")

                filtered_servers.append(server)

        if skipped_servers:
            logger.info(
                f"Circuit breaker: Skipping {len(skipped_servers)} mcp_tools servers: {skipped_servers}"
            )

        if not filtered_servers:
            logger.warning(
                "All mcp_tools servers are blocked by circuit breaker - "
                "consider checking server configurations or waiting for recovery timeout"
            )
        else:
            logger.debug(
                f"Circuit breaker: Allowing {len(filtered_servers)}/{len(servers)} mcp_tools servers"
            )

        return filtered_servers

    def _should_include_http_server(self, server_name: str) -> bool:
        """Check if HTTP server should be included based on circuit breaker state.

        Returns:
            True if server should be included, False if it should be skipped
        """
        if not self._circuit_breakers_enabled or not self._http_circuit_breaker:
            logger.debug(f"Circuit breaker not enabled for HTTP server {server_name}")
            return True

        try:
            should_skip = self._http_circuit_breaker.should_skip_server(server_name)

            if should_skip:
                logger.info(f"Circuit breaker: Skipping HTTP MCP server {server_name}")
                return False
            else:
                logger.debug(f"Circuit breaker: Allowing HTTP MCP server {server_name}")
                return True
        except Exception as cb_error:
            logger.warning(f"Circuit breaker should_skip_server failed for HTTP server {server_name}: {cb_error}")
            return True

    async def _record_mcp_tools_success(self, servers: List[Dict[str, Any]]) -> None:
        """Record successful connection for mcp_tools servers in circuit breaker."""
        await self._record_mcp_tools_event(servers, event="success")

    async def _record_mcp_tools_failure(
        self, servers: List[Dict[str, Any]], error_message: str
    ) -> None:
        """Record connection failure for mcp_tools servers in circuit breaker."""
        await self._record_mcp_tools_event(servers, event="failure", error_message=error_message)

    async def _record_mcp_tools_event(
        self,
        servers: List[Dict[str, Any]],
        event: Literal["success", "failure"],
        error_message: Optional[str] = None,
    ) -> None:
        """Record success/failure for mcp_tools servers in circuit breaker."""
        if not self._circuit_breakers_enabled or not self._mcp_tools_circuit_breaker:
            return

        count = 0
        for server in servers:
            server_name = server.get("name", "unnamed")
            try:
                if event == "success":
                    self._mcp_tools_circuit_breaker.record_success(server_name)
                else:
                    self._mcp_tools_circuit_breaker.record_failure(server_name)
                count += 1
            except Exception as cb_error:
                logger.warning(
                    f"Circuit breaker record_{event} failed for mcp_tools server {server_name}: {cb_error}"
                )

        if count > 0:
            if event == "success":
                logger.debug(
                    f"Circuit breaker: Recorded success for {count} mcp_tools servers"
                )
            else:
                logger.warning(
                    f"Circuit breaker: Recorded failure for {count} mcp_tools servers. Error: {error_message}"
                )

    async def _record_http_server_failure(self, server_name: str, error_message: str) -> None:
        """Record failure for HTTP server in circuit breaker."""
        if not self._circuit_breakers_enabled or not self._http_circuit_breaker:
            return

        try:
            self._http_circuit_breaker.record_failure(server_name)
            logger.warning(
                f"Circuit breaker: Recorded failure for HTTP server {server_name}. Error: {error_message}"
            )
        except Exception as cb_error:
            logger.warning(f"Circuit breaker record_failure failed for HTTP server {server_name}: {cb_error}")

    async def get_circuit_breaker_status(self) -> Dict[str, Dict[str, Any]]:
        """Get current status of all circuit breakers.

        Returns:
            Dict with circuit breaker status for each transport type:
            {
                "mcp_tools": {
                    "enabled": bool,
                    "servers": {
                        "server_name": {
                            "would_skip": bool
                        }
                    }
                },
                "http": {...}
            }
        """
        status = {
            "mcp_tools": {
                "enabled": self._circuit_breakers_enabled and self._mcp_tools_circuit_breaker is not None,
                "servers": {}
            },
            "http": {
                "enabled": self._circuit_breakers_enabled and self._http_circuit_breaker is not None,
                "servers": {}
            }
        }

        # Get mcp_tools circuit breaker status
        if self._mcp_tools_circuit_breaker:
            for server in self._mcp_tools_servers:
                server_name = server.get("name", "unnamed")
                try:
                    would_skip = self._mcp_tools_circuit_breaker.should_skip_server(server_name)
                    status["mcp_tools"]["servers"][server_name] = {
                        "would_skip": would_skip
                    }
                except Exception as cb_error:
                    logger.warning(f"Circuit breaker should_skip_server failed for mcp_tools server {server_name}: {cb_error}")
                    # Default to not skipping if circuit breaker fails
                    status["mcp_tools"]["servers"][server_name] = {
                        "would_skip": False
                    }

        if self._http_circuit_breaker:
            for server in self._http_servers:
                server_name = server.get("name", "unnamed")
                try:
                    would_skip = self._http_circuit_breaker.should_skip_server(server_name)
                    status["http"]["servers"][server_name] = {
                        "would_skip": would_skip
                    }
                except Exception as cb_error:
                    logger.warning(f"Circuit breaker should_skip_server failed for HTTP server {server_name}: {cb_error}")
                    status["http"]["servers"][server_name] = {
                        "would_skip": False
                    }

        return status

    async def _prepare_native_http_mcp_servers(self) -> List[Dict[str, Any]]:
        """Prepare HTTP MCP servers for Claude's native mcp_servers API parameter."""
        if not self._http_servers:
            return []

        native_servers = []
        for server in self._http_servers:
            server_name = server.get("name", "unnamed")
            
            # Apply circuit breaker filtering
            if not await self._should_include_http_server(server_name):
                continue

            server_url = server.get("url")
            if not server_url:
                logger.warning(f"HTTP MCP server {server_name} missing URL, skipping")
                continue

            # Validate HTTPS requirement
            if not server_url.startswith("https://"):
                error_msg = f"HTTP MCP server {server_name} must use HTTPS, got: {server_url}"
                logger.warning(error_msg)
                await self._record_http_server_failure(server_name, error_msg)
                continue

            # Validate URL if validate_url is available
            if validate_url is not None:
                try:
                    validate_url(
                        server_url,
                        allow_localhost=True,
                        allow_private_ips=True
                    )
                except ValueError as e:
                    error_msg = f"Invalid URL for HTTP MCP server {server_name}: {e}"
                    logger.warning(error_msg)
                    await self._record_http_server_failure(server_name, error_msg)
                    continue

            native_server = {
                "type": "url",
                "name": server_name,
                "url": server_url,
            }

            # Add authorization if present
            if "auth_token" in server:
                native_server["auth"] = {
                    "type": "bearer",
                    "token": server["auth_token"]
                }

            # Add tool configuration
            tools_config = {}
            if "enabled" in server:
                tools_config["enabled"] = server["enabled"]
            if "allowed_tools" in server:
                tools_config["allowed_tools"] = server["allowed_tools"]
            
            if tools_config:
                native_server["tools"] = tools_config

            native_servers.append(native_server)

        logger.debug(f"Prepared {len(native_servers)} HTTP MCP servers for Claude native API")
        return native_servers

    def _convert_mcp_tools_to_claude_format(self) -> List[Dict[str, Any]]:
        """Convert MCP tools from stdio/streamable-http servers to Claude format."""
        if not self.functions:
            return []

        # Convert Function objects to tool dictionaries
        tool_dicts = []
        for function in self.functions.values():
            tool_dict = {
                "name": function.name,
                "description": function.description,
                "parameters": function.parameters,
            }
            tool_dicts.append(tool_dict)

        converted_tools = self.convert_tools_to_claude_format(tool_dicts)
        
        logger.debug(f"Converted {len(converted_tools)} MCP tools to Claude format")
        return converted_tools

    async def _should_include_http_server(self, server_name: str) -> bool:
        """Check if HTTP server should be included based on circuit breaker state.

        Returns:
            True if server should be included, False if it should be skipped
        """
        if not self._circuit_breakers_enabled or not self._http_circuit_breaker:
            logger.debug(f"Circuit breaker not enabled for HTTP server {server_name}")
            return True

        try:
            should_skip = self._http_circuit_breaker.should_skip_server(server_name)

            if should_skip:
                logger.info(f"Circuit breaker: Skipping HTTP MCP server {server_name}")
                return False
            else:
                logger.debug(f"Circuit breaker: Allowing HTTP MCP server {server_name}")
                return True
        except Exception as cb_error:
            logger.warning(f"Circuit breaker should_skip_server failed for HTTP server {server_name}: {cb_error}")
            # Default to allowing the server if circuit breaker fails
            return True

    async def _record_http_server_failure(self, server_name: str, error_message: str) -> None:
        """Record failure for HTTP server in circuit breaker."""
        if not self._circuit_breakers_enabled or not self._http_circuit_breaker:
            return

        try:
            self._http_circuit_breaker.record_failure(server_name)
            logger.warning(
                f"Circuit breaker: Recorded failure for HTTP server {server_name}. Error: {error_message}"
            )
        except Exception as cb_error:
            logger.warning(f"Circuit breaker record_failure failed for HTTP server {server_name}: {cb_error}")

    async def cleanup_mcp(self) -> None:
        """Clean up MCP client connections and reset state."""
        if self._mcp_client:
            try:
                await self._mcp_client.disconnect()
                logger.debug("MCP client disconnected successfully")
            except Exception as e:
                logger.warning(f"Error disconnecting MCP client: {e}")
            finally:
                self._mcp_client = None
                self._mcp_initialized = False
                self.functions.clear()
                logger.debug("MCP state reset")

    async def __aenter__(self) -> "ClaudeBackend":
        """Async context manager entry."""
        # Initialize MCP tools if configured
        if self._mcp_tools_servers and not self._mcp_initialized:
            try:
                await self._setup_mcp_tools()
            except Exception as e:
                logger.warning(f"MCP setup failed during context entry: {e}")
        return self

    async def __aexit__(
        self,
        exc_type: Optional[type],
        exc_val: Optional[BaseException],
        exc_tb: Optional[object],
    ) -> None:
        """Async context manager exit with automatic resource cleanup."""
        try:
            await self.cleanup_mcp()
        except Exception as e:
            logger.error(f"Error during ClaudeBackend cleanup: {e}")
            # Don't suppress the original exception if one occurred
            return False

    def convert_tools_to_claude_format(
        self, tools: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Convert tools to Claude's expected format.

        Input formats supported:
        - Response API format: {"type": "function", "name": ..., "description": ..., "parameters": ...}
        - Chat Completions format: {"type": "function", "function": {"name": ..., "description": ..., "parameters": ...}}

        Claude format: {"type": "function", "name": ..., "description": ..., "input_schema": ...}
        """
        if not tools:
            return tools

        converted_tools = []
        for tool in tools:
            if tool.get("type") == "function":
                if "function" in tool:
                    # Chat Completions format -> Claude custom tool
                    func = tool["function"]
                    converted_tools.append(
                        {
                            "type": "custom",
                            "name": func["name"],
                            "description": func["description"],
                            "input_schema": func.get("parameters", {}),
                        }
                    )
                elif "name" in tool and "description" in tool:
                    # Response API format -> Claude custom tool
                    converted_tools.append(
                        {
                            "type": "custom",
                            "name": tool["name"],
                            "description": tool["description"],
                            "input_schema": tool.get("parameters", {}),
                        }
                    )
                else:
                    converted_tools.append(tool)
            else:
                converted_tools.append(tool)

        return converted_tools

    def convert_messages_to_claude_format(
        self, messages: List[Dict[str, Any]]
    ) -> tuple:
        """Convert messages to Claude's expected format.

        Handle different tool message formats and extract system message:
        - Chat Completions tool message: {"role": "tool", "tool_call_id": "...", "content": "..."}
        - Response API tool message: {"type": "function_call_output", "call_id": "...", "output": "..."}
        - System messages: Extract and return separately for top-level system parameter

        Returns:
            tuple: (converted_messages, system_message)
        """
        converted_messages = []
        system_message = ""

        for message in messages:
            if message.get("role") == "system":
                # Extract system message for top-level parameter
                system_message = message.get("content", "")
            elif message.get("role") == "tool":
                # Chat Completions tool message -> Claude tool result
                converted_messages.append(
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "tool_result",
                                "tool_use_id": message.get("tool_call_id"),
                                "content": message.get("content", ""),
                            }
                        ],
                    }
                )
            elif message.get("type") == "function_call_output":
                # Response API tool message -> Claude tool result
                converted_messages.append(
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "tool_result",
                                "tool_use_id": message.get("call_id"),
                                "content": message.get("output", ""),
                            }
                        ],
                    }
                )
            elif message.get("role") == "assistant" and "tool_calls" in message:
                # Assistant message with tool calls - convert to Claude format
                content = []

                # Add text content if present
                if message.get("content"):
                    content.append({"type": "text", "text": message["content"]})

                # Convert tool calls to Claude tool use format
                for tool_call in message["tool_calls"]:
                    tool_name = self.extract_tool_name(tool_call)
                    tool_args = self.extract_tool_arguments(tool_call)
                    tool_id = self.extract_tool_call_id(tool_call)

                    content.append(
                        {
                            "type": "tool_use",
                            "id": tool_id,
                            "name": tool_name,
                            "input": tool_args,
                        }
                    )

                converted_messages.append({"role": "assistant", "content": content})
            elif message.get("role") in ["user", "assistant"]:
                # Keep user and assistant messages, skip system
                converted_message = dict(message)
                if isinstance(converted_message.get("content"), str):
                    # Claude expects content to be text for simple messages
                    pass
                converted_messages.append(converted_message)

        return converted_messages, system_message

    async def stream_with_tools(
        self, messages: List[Dict[str, Any]], tools: List[Dict[str, Any]], **kwargs
    ) -> AsyncGenerator[StreamChunk, None]:
        """Stream response using Claude's Messages API with full multi-tool support."""
        try:
            import anthropic

            # Setup MCP tools if configured and not already initialized
            if self._mcp_tools_servers and not self._mcp_initialized:
                try:
                    await self._setup_mcp_tools()
                except Exception as e:
                    logger.warning(f"MCP setup failed, continuing without MCP: {e}")

            # Merge constructor config with stream kwargs (stream kwargs take priority)
            all_params = {**self.config, **kwargs}

            # Extract framework-specific parameters
            enable_web_search = all_params.get("enable_web_search", False)
            enable_code_execution = all_params.get("enable_code_execution", False)

            # Convert messages to Claude format and extract system message
            converted_messages, system_message = self.convert_messages_to_claude_format(
                messages
            )

            # Combine all tool types (Claude's key advantage!)
            combined_tools = []

            # Add server-side tools if enabled (use correct Claude format)
            if enable_web_search:
                combined_tools.append(
                    {"type": "web_search_20250305", "name": "web_search"}
                )

            if enable_code_execution:
                combined_tools.append(
                    {"type": "code_execution_20250522", "name": "code_execution"}
                )

            # Add user-defined tools
            if tools:
                converted_tools = self.convert_tools_to_claude_format(tools)
                combined_tools.extend(converted_tools)

            # Add MCP tools from stdio/streamable-http servers
            mcp_tools = self._convert_mcp_tools_to_claude_format()
            if mcp_tools:
                combined_tools.extend(mcp_tools)

            # Prepare native HTTP MCP servers
            native_mcp_servers = await self._prepare_native_http_mcp_servers()

            # Build API parameters
            api_params = {
                "messages": converted_messages,
                "stream": True,
            }

            if system_message:
                api_params["system"] = system_message

            if combined_tools:
                api_params["tools"] = combined_tools

            # Add native HTTP MCP servers if available
            if native_mcp_servers:
                api_params["mcp_servers"] = native_mcp_servers

            # Direct passthrough of all parameters except those handled separately
            excluded_params = {
                "enable_web_search",
                "enable_code_execution",
                "agent_id",
                "session_id",
                "allowed_tools",
                "exclude_tools",
                "max_mcp_message_history",
                "mcp_servers",
                "type", 
            }
            for key, value in all_params.items():
                if key not in excluded_params and value is not None:
                    api_params[key] = value

            # Claude API requires max_tokens - add default if not provided
            if "max_tokens" not in api_params:
                api_params["max_tokens"] = 4096

            # Conditional beta headers logic - only add when actually needed
            beta_features = []

            # Add MCP beta header only if HTTP native MCP servers are present
            if native_mcp_servers:
                beta_features.append("mcp-client-2025-04-04")

            # Add code execution beta header if enabled  
            if enable_code_execution:
                beta_features.append("code-execution-2025-05-22")

            # Set up client with conditional headers
            if beta_features:
                default_headers = {"anthropic-beta": ",".join(beta_features)}
                client = anthropic.AsyncAnthropic(
                    api_key=self.api_key,
                    default_headers=default_headers
                )
            else:
                client = anthropic.AsyncAnthropic(api_key=self.api_key)
            # Create stream
            if enable_code_execution:
                # Code execution uses beta client
                stream = await client.beta.messages.create(**api_params)
            else:
                # Regular client
                stream = await client.messages.create(**api_params)

            content = ""
            current_tool_uses = {}

            async for event in stream:
                try:
                    if event.type == "message_start":
                        # Message started
                        continue

                    elif event.type == "content_block_start":
                        # Content block started (text, tool use, or tool result)
                        if hasattr(event, "content_block"):
                            if event.content_block.type == "tool_use":
                                # Regular tool use started (user-defined or MCP tools)
                                tool_id = event.content_block.id
                                tool_name = event.content_block.name
                                current_tool_uses[tool_id] = {
                                    "id": tool_id,
                                    "name": tool_name,
                                    "input": "",  # Will accumulate JSON fragments
                                    "index": getattr(event, "index", None),
                                }
                            elif event.content_block.type == "server_tool_use":
                                # Server-side tool use (code execution, web search) - show status immediately
                                tool_id = event.content_block.id
                                tool_name = event.content_block.name
                                current_tool_uses[tool_id] = {
                                    "id": tool_id,
                                    "name": tool_name,
                                    "input": "",  # Will accumulate JSON fragments
                                    "index": getattr(event, "index", None),
                                    "server_side": True,
                                }

                                # Show tool execution starting
                                if tool_name == "code_execution":
                                    yield StreamChunk(
                                        type="content",
                                        content=f"\n💻 [Code Execution] Starting...\n",
                                    )
                                elif tool_name == "web_search":
                                    yield StreamChunk(
                                        type="content",
                                        content=f"\n🔍 [Web Search] Starting search...\n",
                                    )
                            elif (
                                event.content_block.type == "code_execution_tool_result"
                            ):
                                # Code execution result - format properly
                                result_block = event.content_block

                                # Format execution result nicely
                                result_parts = []
                                if (
                                    hasattr(result_block, "stdout")
                                    and result_block.stdout
                                ):
                                    result_parts.append(
                                        f"Output: {result_block.stdout.strip()}"
                                    )
                                if (
                                    hasattr(result_block, "stderr")
                                    and result_block.stderr
                                ):
                                    result_parts.append(
                                        f"Error: {result_block.stderr.strip()}"
                                    )
                                if (
                                    hasattr(result_block, "return_code")
                                    and result_block.return_code != 0
                                ):
                                    result_parts.append(
                                        f"Exit code: {result_block.return_code}"
                                    )

                                if result_parts:
                                    result_text = f"\n💻 [Code Execution Result]\n{chr(10).join(result_parts)}\n"
                                    yield StreamChunk(
                                        type="content", content=result_text
                                    )

                    elif event.type == "content_block_delta":
                        # Content streaming
                        if hasattr(event, "delta"):
                            if event.delta.type == "text_delta":
                                # Text content
                                text_chunk = event.delta.text
                                content += text_chunk
                                yield StreamChunk(type="content", content=text_chunk)

                            elif event.delta.type == "input_json_delta":
                                # Tool input streaming - accumulate JSON fragments
                                if hasattr(event, "index"):
                                    # Find tool by index
                                    for tool_id, tool_data in current_tool_uses.items():
                                        if tool_data.get("index") == event.index:
                                            # Accumulate partial JSON
                                            partial_json = getattr(
                                                event.delta, "partial_json", ""
                                            )
                                            tool_data["input"] += partial_json
                                            break

                    elif event.type == "content_block_stop":
                        # Content block completed - check if it was a server-side tool
                        if hasattr(event, "index"):
                            # Find the tool that just completed
                            for tool_id, tool_data in current_tool_uses.items():
                                if tool_data.get(
                                    "index"
                                ) == event.index and tool_data.get("server_side"):
                                    tool_name = tool_data.get("name", "")

                                    # Parse the accumulated input to show what was executed
                                    tool_input = tool_data.get("input", "")
                                    try:
                                        if tool_input:
                                            parsed_input = json.loads(tool_input)
                                        else:
                                            parsed_input = {}
                                    except json.JSONDecodeError:
                                        parsed_input = {"raw_input": tool_input}

                                    if tool_name == "code_execution":
                                        code = parsed_input.get("code", "")
                                        if code:
                                            yield StreamChunk(
                                                type="content",
                                                content=f"💻 [Code] {code}\n",
                                            )
                                        yield StreamChunk(
                                            type="content",
                                            content=f"✅ [Code Execution] Completed\n",
                                        )

                                        # Yield tool result as content
                                        yield StreamChunk(
                                            type="content",
                                            content=f"🔧 Code Execution [Completed]: {code}",
                                        )

                                    elif tool_name == "web_search":
                                        query = parsed_input.get("query", "")
                                        if query:
                                            yield StreamChunk(
                                                type="content",
                                                content=f"🔍 [Query] '{query}'\n",
                                            )
                                        yield StreamChunk(
                                            type="content",
                                            content=f"✅ [Web Search] Completed\n",
                                        )

                                        # Yield tool result as content
                                        yield StreamChunk(
                                            type="content",
                                            content=f"🔧 Web Search [Completed]: {query}",
                                        )

                                    # Mark this tool as processed so we don't duplicate it later
                                    tool_data["processed"] = True
                                    break

                    elif event.type == "message_delta":
                        # Message metadata updates (usage, etc.)
                        if hasattr(event, "usage"):
                            # Track token usage
                            pass

                    elif event.type == "message_stop":
                        # Message completed - build final response

                        # Handle any completed tool uses
                        if current_tool_uses:
                            # Process both user-defined tools and MCP tools that need external execution
                            user_tool_calls = []

                            for tool_use in current_tool_uses.values():
                                tool_name = tool_use.get("name", "")
                                is_server_side = tool_use.get("server_side", False)

                                # Process tools that need external execution (user-defined + MCP)
                                if not is_server_side and tool_name not in [
                                    "web_search",
                                    "code_execution",
                                ]:
                                    # Parse accumulated JSON input
                                    tool_input = tool_use.get("input", "")
                                    try:
                                        if tool_input:
                                            parsed_input = json.loads(tool_input)
                                        else:
                                            parsed_input = {}
                                    except json.JSONDecodeError:
                                        parsed_input = {"raw_input": tool_input}

                                    # Check if this is an MCP tool that needs execution
                                    if tool_name in self.functions:
                                        # Execute MCP tool directly
                                        try:
                                            function = self.functions[tool_name]
                                            result = await function.entrypoint(json.dumps(parsed_input))
                                            
                                            # Create tool result message for MCP execution
                                            tool_result = self.create_tool_result_message(
                                                tool_use, str(result)
                                            )
                                            yield StreamChunk(
                                                type="content",
                                                content=f"🔧 MCP Tool [{tool_name}] Result: {result}",
                                            )
                                        except Exception as e:
                                            logger.error(f"MCP tool execution failed for {tool_name}: {e}")
                                            yield StreamChunk(
                                                type="content",
                                                content=f"❌ MCP Tool [{tool_name}] Error: {e}",
                                            )
                                    else:
                                        # Regular user-defined tool - add to tool calls for external execution
                                        user_tool_calls.append(
                                            {
                                                "id": tool_use["id"],
                                                "type": "function",
                                                "function": {
                                                    "name": tool_name,
                                                    "arguments": parsed_input,
                                                },
                                            }
                                        )

                            # Yield user tool calls if any (non-MCP tools)
                            if user_tool_calls:
                                yield StreamChunk(
                                    type="tool_calls", tool_calls=user_tool_calls
                                )

                            # Build complete message with only user tool calls (builtin and MCP tools are handled separately)
                            complete_message = {
                                "role": "assistant",
                                "content": content.strip(),
                            }
                            if user_tool_calls:
                                complete_message["tool_calls"] = user_tool_calls
                            yield StreamChunk(
                                type="complete_message",
                                complete_message=complete_message,
                            )
                        else:
                            # Regular text response
                            complete_message = {
                                "role": "assistant",
                                "content": content.strip(),
                            }
                            yield StreamChunk(
                                type="complete_message",
                                complete_message=complete_message,
                            )

                        # Track usage for pricing
                        if enable_web_search:
                            self.search_count += 1  # Approximate search usage

                        if enable_code_execution:
                            self.code_session_hours += 0.083  # 5 min minimum session

                        yield StreamChunk(type="done")
                        return

                except Exception as event_error:
                    yield StreamChunk(
                        type="error", error=f"Event processing error: {event_error}"
                    )
                    continue

        except Exception as e:
            yield StreamChunk(type="error", error=f"Claude API error: {e}")

    def get_provider_name(self) -> str:
        """Get the provider name."""
        return "Claude"

    def get_supported_builtin_tools(self) -> List[str]:
        """Get list of builtin tools supported by Claude.
        
        Note: MCP tools are external integrations, not builtin tools.
        Only returns actual Claude builtin tools.
        """
        return ["web_search", "code_execution"]

    def extract_tool_name(self, tool_call: Dict[str, Any]) -> str:
        """Extract tool name from tool call (handles multiple formats)."""
        # Chat Completions format
        if "function" in tool_call:
            return tool_call.get("function", {}).get("name", "unknown")
        # Claude native format
        elif "name" in tool_call:
            return tool_call.get("name", "unknown")
        # Fallback
        return "unknown"

    def extract_tool_arguments(self, tool_call: Dict[str, Any]) -> Dict[str, Any]:
        """Extract tool arguments from tool call (handles multiple formats)."""
        # Chat Completions format
        if "function" in tool_call:
            args = tool_call.get("function", {}).get("arguments", {})
        # Claude native format
        elif "input" in tool_call:
            args = tool_call.get("input", {})
        else:
            args = {}

        # Ensure JSON parsing if needed
        if isinstance(args, str):
            try:
                return json.loads(args)
            except:
                return {}
        return args

    def extract_tool_call_id(self, tool_call: Dict[str, Any]) -> str:
        """Extract tool call ID from tool call."""
        return tool_call.get("id") or tool_call.get("call_id") or ""

    def create_tool_result_message(
        self, tool_call: Dict[str, Any], result_content: str
    ) -> Dict[str, Any]:
        """Create tool result message in Claude's expected format."""
        tool_call_id = self.extract_tool_call_id(tool_call)
        return {
            "role": "user",
            "content": [
                {
                    "type": "tool_result",
                    "tool_use_id": tool_call_id,
                    "content": result_content,
                }
            ],
        }

    def extract_tool_result_content(self, tool_result_message: Dict[str, Any]) -> str:
        """Extract content from Claude tool result message."""
        content = tool_result_message.get("content", [])
        if isinstance(content, list) and content:
            for item in content:
                if isinstance(item, dict) and item.get("type") == "tool_result":
                    return item.get("content", "")
        return ""

    def estimate_tokens(self, text: str) -> int:
        """Estimate token count for text (Claude uses ~4 chars per token)."""
        return len(text) // 4

    def calculate_cost(
        self, input_tokens: int, output_tokens: int, model: str
    ) -> float:
        """Calculate cost for Claude token usage (2025 pricing)."""
        model_lower = model.lower()

        if "claude-4" in model_lower:
            if "opus" in model_lower:
                # Claude 4 Opus
                input_cost = (input_tokens / 1_000_000) * 15.0
                output_cost = (output_tokens / 1_000_000) * 75.0
            else:
                # Claude 4 Sonnet
                input_cost = (input_tokens / 1_000_000) * 3.0
                output_cost = (output_tokens / 1_000_000) * 15.0
        elif "claude-3.7" in model_lower or "claude-3-7" in model_lower:
            # Claude 3.7 Sonnet
            input_cost = (input_tokens / 1_000_000) * 3.0
            output_cost = (output_tokens / 1_000_000) * 15.0
        elif "claude-3.5" in model_lower or "claude-3-5" in model_lower:
            if "haiku" in model_lower:
                # Claude 3.5 Haiku
                input_cost = (input_tokens / 1_000_000) * 1.0
                output_cost = (output_tokens / 1_000_000) * 5.0
            else:
                # Claude 3.5 Sonnet (legacy)
                input_cost = (input_tokens / 1_000_000) * 3.0
                output_cost = (output_tokens / 1_000_000) * 15.0
        else:
            # Default fallback (assume Claude 4 Sonnet pricing)
            input_cost = (input_tokens / 1_000_000) * 3.0
            output_cost = (output_tokens / 1_000_000) * 15.0

        # Add tool usage costs
        tool_costs = 0.0
        if self.search_count > 0:
            tool_costs += (self.search_count / 1000) * 10.0  # $10 per 1,000 searches

        if self.code_session_hours > 0:
            tool_costs += self.code_session_hours * 0.05  # $0.05 per session-hour

        return input_cost + output_cost + tool_costs

    def reset_tool_usage(self):
        """Reset tool usage tracking."""
        self.search_count = 0
        self.code_session_hours = 0.0
        super().reset_token_usage()
