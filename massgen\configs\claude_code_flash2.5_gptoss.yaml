# Single agent configuration using Claude Code Stream backend
# This demonstrates integration with claude-code-sdk-python for native Claude Code capabilities

agents:
  - id: "claude_code_agent"
    backend:
      type: "claude_code"
      cwd: "claude_code_workspace"  # Working directory for file operations
      # model: "claude-sonnet-4-20250514"

      # Claude Code Stream specific options
      # append_system_prompt: "You are a helpful AI assistant powered by Claude Code. You have access to a comprehensive set of development tools including file operations, code execution, web search, and more. Use these capabilities thoughtfully to provide the best assistance."
      # max_turns: 3 # Allow unlimited conversation turns

      # Tool configuration (<PERSON>'s native tools)
      # allowed_tools:
      #   - "Read" # Read files from filesystem
      #   - "Write" # Write files to filesystem
      #   - "Edit" # Edit existing files
      #   - "MultiEdit" # Multiple edits in one operation
      #   - "Bash" # Execute shell commands
      #   - "Grep" # Search within files
      #   - "Glob" # Find files by pattern
      #   - "LS" # List directory contents
      #   - "WebSearch" # Search the web
      #   - "WebFetch" # Fetch web content
      #   - "TodoWrite" # Task management
      #   - "NotebookEdit" # Jupyter notebook editing
      #   # MCP tools (if available)
      #   - "mcp__ide__getDiagnostics"
      #   - "mcp__ide__executeCode"

  - id: "gemini-2.5-flash"
    backend:
      type: "gemini"
      model: "gemini-2.5-flash"
      enable_web_search: true
      enable_code_execution: true

  - id: "gpt-oss-120b"
    backend:
      type: "chatcompletion"
      model: "gpt-oss-120b"
      base_url: "https://api.cerebras.ai/v1"

# UI Configuration
ui:
  type: "rich_terminal"
  logging_enabled: true
