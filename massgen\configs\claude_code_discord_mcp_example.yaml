agent:
  id: "claude_code_discord_mcp"
  backend:
    type: "claude_code"
    cwd: "claude_code_workspace_discord_mcp"
    permission_mode: "bypassPermissions"
    
    # Discord MCP server 
    mcp_servers:
      discord:
        type: "stdio"
        command: "npx"
        args: ["-y", "mcp-discord", "--config", "YOUR_DISCORD_TOKEN"]

    allowed_tools:
      - "Read"
      - "Write"
      - "Bash"
      - "LS"
      - "WebSearch"
      # MCP tools will be auto-discovered from the server

ui:
  display_type: "rich_terminal"
  logging_enabled: true