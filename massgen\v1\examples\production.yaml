# MassGen Configuration: Production
#
# Optimized for production use with reliable, high-quality results.
# Uses robust models with strict consensus requirements and comprehensive logging.
#
# Usage:
#   python -m massgen --config examples/production.yaml "Production question"

orchestrator:
  max_duration: 900           # 15 minutes for thorough analysis
  consensus_threshold: 0.5    # 50% agreement for high confidence
  max_debate_rounds: 2        # Standard debate rounds
  status_check_interval: 1.0  # Balanced responsiveness
  thread_pool_timeout: 10     # Longer timeout for stability

agents:
  # Agent 1: OpenAI o4-mini (Primary analyst)
  - agent_id: 1
    agent_type: "openai"
    model_config:
      model: "o4-mini"
      tools:
        - live_search
        - code_execution
      max_retries: 15           # More retries for reliability
      max_rounds: 5           # Allow thorough analysis
      inference_timeout: 180  # Longer timeout for complex tasks
      stream: true

  # Agent 2: Google Gemini Pro (Research specialist)
  - agent_id: 2
    agent_type: "gemini"
    model_config:
      model: "gemini-2.5-pro"
      tools:
        - live_search
        - code_execution
      max_retries: 10
      max_rounds: 5
      temperature: 0.4         # Balanced creativity and reliability
      top_p: 0.9
      inference_timeout: 180
      stream: true

  # Agent 3: x<PERSON><PERSON>rok (Alternative perspective)
  - agent_id: 3
    agent_type: "grok"
    model_config:
      model: "grok-4"
      tools: ["live_search"]
      max_retries: 12
      max_rounds: 10
      temperature: 0.2        # Very low temperature for accuracy
      top_p: 0.9
      inference_timeout: 180
      stream: true

streaming_display:
  display_enabled: true
  max_lines: 10               # More lines for detailed output
  save_logs: true

logging:
  log_dir: "logs/production"
  non_blocking: false         # Ensure all logs are saved

task:
  category: "production"
  domain: "business"
  complexity: "high" 