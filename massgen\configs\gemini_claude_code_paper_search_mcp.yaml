# uv run python -m massgen.cli --config massgen/configs/gemini_claude_code_paper_search_mcp.yaml "search 5 papers which are related to multi-agent scaling system Massgen, download them and list their title in your answer"
agents:
  - id: "claude_code_paper_search_mcp"
    backend:
      type: "claude_code"
      cwd: "claude_code_paper_search_workspace"
      permission_mode: "bypassPermissions"
      
      # Paper Search MCP server configuration
      mcp_servers:
        paper_search:
          type: "stdio"
          command: "uv"
          args: ["run", "-m", "paper_search_mcp.server"]
          env:
            SEMANTIC_SCHOLAR_API_KEY: ""  # Optional: Add your API key here for enhanced features
          
      allowed_tools:
        - "Read"
        - "Write"
        - "Bash"
        - "LS"
        - "WebSearch"
        # MCP tools will be auto-discovered:
        # - mcp__paper_search__search_arxiv
        # - mcp__paper_search__download_arxiv
        # - mcp__paper_search__search_pubmed
        # - mcp__paper_search__download_pubmed
        # - mcp__paper_search__search_biorxiv
        # - mcp__paper_search__download_biorxiv
        # - mcp__paper_search__search_google_scholar
        # - mcp__paper_search__search_semantic_scholar
        # - mcp__paper_search__search_iacr
        
    system_message: |
      You are an AI research assistant integrated with the Paper Search MCP server.
      **IMPORTANT: you MUST consider and MUST synthesize the provided answer by other agents (even the searched paper list by other agents). Then provide your own answer.**

  - id: "gemini2.5flash"
    backend:
      type: "gemini"
      model: "gemini-2.5-flash"
      enable_web_search: true
    system_message: |
      You are a helpful AI assistant with web search and code execution capabilities. You should always complete tasks that you can do. If you cannot complete a task, just omit it.  
      **IMPORTANT: you should synthesize the provided answers by other agents, then provide your own answer.**

ui:
  display_type: "rich_terminal"
  logging_enabled: true