# MassGen Configuration: Single Agent Mode
#
# Simple configuration for single-agent processing.
# Ideal for straightforward tasks that don't require multi-agent collaboration.
#
# Usage:
#   uv run python -m massgen.v1.cli --config examples/single_agent.yaml "Simple question"

orchestrator:
  max_duration: 300           # 5 minutes - shorter for simple tasks
  consensus_threshold: 1.0    # Not used in single agent mode
  max_debate_rounds: 0        # Not used in single agent mode
  status_check_interval: 1.0
  thread_pool_timeout: 5

agents:
  # Single Agent: OpenAI GPT-4o
  - agent_id: 1
    agent_type: "openai"
    model_config:
      model: "o4-mini"
      tools: ["live_search", "code_execution"]
      max_retries: 10
      max_rounds: 10            # Fewer rounds for simple processing
      max_tokens: 4000          # Reasonable token limit
      temperature: 0.7          # Balanced creativity
      top_p: 0.9
      inference_timeout: 120   # 2 minutes timeout
      stream: false

streaming_display:
  display_enabled: true         # Still show progress
  max_lines: 10                 # Smaller display for single agent
  save_logs: true

logging:
  log_dir: "logs"
  session_id: null              # Auto-generate
  non_blocking: true 